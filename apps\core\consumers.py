import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from .models import TabSession, TaskStatus


class TabConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer cho real-time updates của tab"""
    
    async def connect(self):
        self.tab_id = self.scope['url_route']['kwargs']['tab_id']
        self.tab_group_name = f'tab_{self.tab_id}'
        
        # Join tab group
        await self.channel_layer.group_add(
            self.tab_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Send initial tab status
        tab_status = await self.get_tab_status()
        if tab_status:
            await self.send(text_data=json.dumps({
                'type': 'tab_status',
                'data': tab_status
            }))
    
    async def disconnect(self, close_code):
        # Leave tab group
        await self.channel_layer.group_discard(
            self.tab_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        """Handle messages from WebSocket"""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong'
                }))
            elif message_type == 'get_status':
                tab_status = await self.get_tab_status()
                await self.send(text_data=json.dumps({
                    'type': 'tab_status',
                    'data': tab_status
                }))
                
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON'
            }))
    
    # Group message handlers
    async def task_update(self, event):
        """Handle task update messages"""
        await self.send(text_data=json.dumps({
            'type': 'task_update',
            'data': event['data']
        }))
    
    async def tab_update(self, event):
        """Handle tab update messages"""
        await self.send(text_data=json.dumps({
            'type': 'tab_update',
            'data': event['data']
        }))
    
    async def log_message(self, event):
        """Handle log messages"""
        await self.send(text_data=json.dumps({
            'type': 'log_message',
            'data': event['data']
        }))
    
    @database_sync_to_async
    def get_tab_status(self):
        """Get current tab status from database"""
        try:
            tab = TabSession.objects.filter(
                tab_id=self.tab_id,
                is_active=True
            ).first()
            
            if not tab:
                return None
            
            # Get active task if any
            task_status = TaskStatus.objects.filter(
                tab_id=self.tab_id,
                status__in=['pending', 'running']
            ).first()
            
            result = {
                'tab_id': str(tab.tab_id),
                'program_name': tab.program_name,
                'display_name': tab.program_display_name,
                'tab_state': tab.tab_state,
                'task_status': None
            }
            
            if task_status:
                result['task_status'] = {
                    'task_id': task_status.task_id,
                    'task_name': task_status.task_name,
                    'status': task_status.status,
                    'progress': task_status.progress,
                    'current_step': task_status.current_step,
                    'result_data': task_status.result_data,
                }
            
            return result
            
        except Exception as e:
            return {'error': str(e)}
