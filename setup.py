#!/usr/bin/env python
"""
Setup script for Multi-Program WebUI
"""

import os
import sys
import subprocess
import django
from django.core.management import execute_from_command_line

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{'='*50}")
    print(f"🔧 {description}")
    print(f"{'='*50}")
    
    try:
        if isinstance(command, list):
            result = subprocess.run(command, check=True, capture_output=True, text=True)
        else:
            result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        
        if result.stdout:
            print(result.stdout)
        print(f"✅ {description} completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error in {description}:")
        print(f"Command: {' '.join(command) if isinstance(command, list) else command}")
        print(f"Return code: {e.returncode}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    django.setup()

def main():
    """Main setup function"""
    print("🚀 Setting up Multi-Program WebUI")
    print("This will install dependencies, run migrations, and setup the project")
    
    # Check if we're in the right directory
    if not os.path.exists('manage.py'):
        print("❌ Error: manage.py not found. Please run this script from the project root directory.")
        sys.exit(1)
    
    # Install dependencies
    if not run_command([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      "Installing Python dependencies"):
        print("❌ Failed to install dependencies. Please check your Python environment.")
        sys.exit(1)
    
    # Setup Django
    setup_django()
    
    # Create directories
    directories = [
        'logs',
        'media',
        'media/exports',
        'staticfiles'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 Created directory: {directory}")
    
    # Run Django commands
    django_commands = [
        (['python', 'manage.py', 'makemigrations'], "Creating database migrations"),
        (['python', 'manage.py', 'migrate'], "Running database migrations"),
        (['python', 'manage.py', 'collectstatic', '--noinput'], "Collecting static files"),
    ]
    
    for command, description in django_commands:
        if not run_command(command, description):
            print(f"❌ Failed to {description.lower()}")
            sys.exit(1)
    
    # Create superuser (optional)
    print(f"\n{'='*50}")
    print("👤 Create Django superuser (optional)")
    print(f"{'='*50}")
    
    create_superuser = input("Do you want to create a Django superuser? (y/N): ").lower().strip()
    if create_superuser in ['y', 'yes']:
        try:
            subprocess.run([sys.executable, 'manage.py', 'createsuperuser'], check=True)
            print("✅ Superuser created successfully!")
        except subprocess.CalledProcessError:
            print("⚠️ Superuser creation skipped or failed")
    
    # Setup complete
    print(f"\n{'='*60}")
    print("🎉 Setup completed successfully!")
    print(f"{'='*60}")
    print("\n📋 Next steps:")
    print("1. Start Redis server (required for WebSocket and Celery):")
    print("   redis-server")
    print("\n2. Start Celery worker (in a new terminal):")
    print("   celery -A celery_app worker --loglevel=info")
    print("\n3. Start Django development server:")
    print("   python manage.py runserver")
    print("\n4. Open your browser and go to:")
    print("   http://localhost:8000")
    print("\n🔧 For production deployment:")
    print("   - Use gunicorn instead of runserver")
    print("   - Setup nginx for static files")
    print("   - Use supervisor for process management")
    print("   - Configure proper database (PostgreSQL recommended)")

if __name__ == '__main__':
    main()
