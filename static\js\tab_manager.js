/**
 * Tab Manager for Multi-Program WebUI
 */

class TabManager {
    constructor() {
        this.tabs = new Map();
        this.activeTabId = null;
        this.availablePrograms = [];
        this.websockets = new Map();
    }

    init(initialTabs = [], availablePrograms = []) {
        this.availablePrograms = availablePrograms;
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Load initial tabs
        initialTabs.forEach(tab => {
            this.addTab(tab.id, tab.programName, tab.displayName, tab.url, false);
        });
        
        // Update dropdown
        this.updateProgramDropdown();
        
        // Activate first tab if any
        if (initialTabs.length > 0) {
            this.activateTab(initialTabs[0].id);
        }
    }

    setupEventListeners() {
        // Program dropdown click handlers
        $(document).on('click', '.open-program-btn', (e) => {
            const programKey = $(e.target).data('program');
            this.openProgram(programKey);
        });

        // Dropdown item click handlers
        $(document).on('click', '.dropdown-item[data-program]', (e) => {
            e.preventDefault();
            const programKey = $(e.target).closest('.dropdown-item').data('program');
            this.openProgram(programKey);
        });

        // Tab close handlers
        $(document).on('click', '.tab-close', (e) => {
            e.preventDefault();
            e.stopPropagation();
            const tabId = $(e.target).closest('.nav-link').data('tab-id');
            this.closeTab(tabId);
        });

        // Tab click handlers
        $(document).on('click', '.nav-link[data-tab-id]', (e) => {
            e.preventDefault();
            const tabId = $(e.target).closest('.nav-link').data('tab-id');
            this.activateTab(tabId);
        });
    }

    async openProgram(programKey) {
        const program = this.availablePrograms.find(p => p.key === programKey);
        if (!program) {
            console.error('Program not found:', programKey);
            return;
        }

        // Show loading modal
        $('#loadingModal').modal('show');

        try {
            // Create tab via API
            const response = await fetch('/api/tabs/create/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': window.CSRF_TOKEN
                },
                body: JSON.stringify({
                    program_name: programKey
                })
            });

            const data = await response.json();
            
            if (data.success) {
                // Add tab to UI
                this.addTab(data.tab_id, data.program_name, data.display_name, data.url);
                this.activateTab(data.tab_id);
                this.updateProgramDropdown();
            } else {
                throw new Error(data.error || 'Failed to create tab');
            }
        } catch (error) {
            console.error('Error opening program:', error);
            this.showAlert('Lỗi khi mở chương trình: ' + error.message, 'danger');
        } finally {
            $('#loadingModal').modal('hide');
        }
    }

    addTab(tabId, programName, displayName, url, activate = true) {
        // Check if tab already exists
        if (this.tabs.has(tabId)) {
            if (activate) {
                this.activateTab(tabId);
            }
            return;
        }

        // Store tab info
        this.tabs.set(tabId, {
            id: tabId,
            programName: programName,
            displayName: displayName,
            url: url,
            status: 'idle'
        });

        // Create tab navigation
        const tabNav = $(`
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="tab-${tabId}" data-bs-toggle="tab" data-bs-target="#content-${tabId}" 
                        type="button" role="tab" data-tab-id="${tabId}">
                    <span class="tab-status idle"></span>
                    <span class="tab-title">${displayName}</span>
                    <button class="tab-close" type="button" title="Đóng tab">
                        <i class="fas fa-times"></i>
                    </button>
                </button>
            </li>
        `);

        // Create tab content
        const tabContent = $(`
            <div class="tab-pane fade h-100" id="content-${tabId}" role="tabpanel">
                <div class="tab-loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Đang tải ${displayName}...</p>
                </div>
            </div>
        `);

        // Add to DOM
        $('#programTabs').append(tabNav);
        $('#programTabContent').append(tabContent);

        // Hide welcome tab
        $('#welcome-tab').removeClass('show active');

        // Load content in iframe
        setTimeout(() => {
            const iframe = $(`<iframe src="${url}" title="${displayName}"></iframe>`);
            $(`#content-${tabId}`).html(iframe);
        }, 100);

        // Setup WebSocket for this tab
        this.setupTabWebSocket(tabId);

        if (activate) {
            this.activateTab(tabId);
        }
    }

    async closeTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab) return;

        try {
            // Close tab via API
            const response = await fetch('/api/tabs/close/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': window.CSRF_TOKEN
                },
                body: JSON.stringify({
                    tab_id: tabId
                })
            });

            const data = await response.json();
            
            if (data.success) {
                // Remove from UI
                $(`#tab-${tabId}`).closest('.nav-item').remove();
                $(`#content-${tabId}`).remove();
                
                // Disconnect WebSocket
                if (this.websockets.has(tabId)) {
                    this.websockets.get(tabId).disconnect();
                    this.websockets.delete(tabId);
                }
                
                // Remove from tabs map
                this.tabs.delete(tabId);
                
                // Activate another tab or show welcome
                if (this.activeTabId === tabId) {
                    const remainingTabs = Array.from(this.tabs.keys());
                    if (remainingTabs.length > 0) {
                        this.activateTab(remainingTabs[0]);
                    } else {
                        this.showWelcomeTab();
                    }
                }
                
                // Update dropdown
                this.updateProgramDropdown();
            } else {
                throw new Error(data.error || 'Failed to close tab');
            }
        } catch (error) {
            console.error('Error closing tab:', error);
            this.showAlert('Lỗi khi đóng tab: ' + error.message, 'danger');
        }
    }

    activateTab(tabId) {
        if (!this.tabs.has(tabId)) return;

        // Update active tab ID
        this.activeTabId = tabId;

        // Update UI
        $('.nav-link[data-tab-id]').removeClass('active');
        $('.tab-pane').removeClass('show active');
        
        $(`#tab-${tabId}`).addClass('active');
        $(`#content-${tabId}`).addClass('show active');
    }

    showWelcomeTab() {
        this.activeTabId = null;
        $('.nav-link[data-tab-id]').removeClass('active');
        $('.tab-pane').removeClass('show active');
        $('#welcome-tab').addClass('show active');
    }

    setupTabWebSocket(tabId) {
        const wsUrl = `${window.WEBSOCKET_URL}core/tabs/${tabId}/`;
        const ws = window.WebSocketManager.create(`tab_${tabId}`, wsUrl);
        
        // Setup message handlers
        ws.addMessageHandler('task_update', (data) => {
            this.updateTabStatus(tabId, data);
        });

        ws.addMessageHandler('tab_update', (data) => {
            this.updateTabInfo(tabId, data);
        });

        ws.addMessageHandler('log_message', (data) => {
            this.handleLogMessage(tabId, data);
        });

        // Connect
        ws.connect();
        this.websockets.set(tabId, ws);
    }

    updateTabStatus(tabId, statusData) {
        const tab = this.tabs.get(tabId);
        if (!tab) return;

        // Update tab status
        tab.status = statusData.status || 'idle';
        
        // Update UI
        const statusElement = $(`#tab-${tabId} .tab-status`);
        statusElement.removeClass('idle running error').addClass(tab.status);
    }

    updateTabInfo(tabId, data) {
        const tab = this.tabs.get(tabId);
        if (!tab) return;

        // Update tab information
        if (data.display_name) {
            tab.displayName = data.display_name;
            $(`#tab-${tabId} .tab-title`).text(data.display_name);
        }
    }

    handleLogMessage(tabId, data) {
        // Forward log message to iframe if needed
        const iframe = $(`#content-${tabId} iframe`)[0];
        if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage({
                type: 'log_message',
                data: data
            }, '*');
        }
    }

    async updateProgramDropdown() {
        try {
            const response = await fetch('/api/programs/available/');
            const data = await response.json();
            
            const dropdown = $('#programDropdownMenu');
            dropdown.empty();
            
            if (data.programs.length === 0) {
                dropdown.append('<li><span class="dropdown-item-text text-muted">Tất cả chương trình đã được mở</span></li>');
            } else {
                data.programs.forEach(program => {
                    const item = $(`
                        <li>
                            <a class="dropdown-item" href="#" data-program="${program.key}">
                                <div class="program-icon">${program.icon}</div>
                                <div class="program-info">
                                    <div class="program-name">${program.name}</div>
                                    <div class="program-description">${program.description}</div>
                                </div>
                            </a>
                        </li>
                    `);
                    dropdown.append(item);
                });
            }
        } catch (error) {
            console.error('Error updating program dropdown:', error);
        }
    }

    showAlert(message, type = 'info') {
        const alert = $(`
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
        
        // Add to top of container
        $('.container-fluid').prepend(alert);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            alert.alert('close');
        }, 5000);
    }
}

// Global instance
window.TabManager = new TabManager();
