from django.shortcuts import render, redirect
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.conf import settings
from django.contrib.sessions.models import Session
import json
import uuid
from .models import TabSession, ProgramConfig, TaskStatus


def index(request):
    """Trang chủ với tab manager"""
    # <PERSON><PERSON>y danh sách chương trình có sẵn
    programs = []
    for program_key, program_config in settings.PROGRAM_CONFIGS.items():
        programs.append({
            'key': program_key,
            'name': program_config['name'],
            'description': program_config['description'],
            'icon': program_config['icon'],
            'url_name': program_config['url_name'],
        })
    
    # L<PERSON><PERSON> các tab đang mở từ session
    session_key = request.session.session_key
    if not session_key:
        request.session.create()
        session_key = request.session.session_key
    
    active_tabs = TabSession.objects.filter(
        session_key=session_key,
        is_active=True
    ).order_by('created_at')
    
    context = {
        'programs': programs,
        'active_tabs': active_tabs,
    }
    return render(request, 'core/tab_manager.html', context)


@csrf_exempt
@require_http_methods(["POST"])
def create_tab(request):
    """Tạo tab mới cho chương trình"""
    try:
        data = json.loads(request.body)
        program_name = data.get('program_name')
        
        if not program_name or program_name not in settings.PROGRAM_CONFIGS:
            return JsonResponse({'error': 'Invalid program name'}, status=400)
        
        # Ensure session exists
        if not request.session.session_key:
            request.session.create()
        
        program_config = settings.PROGRAM_CONFIGS[program_name]
        
        # Tạo tab mới
        tab = TabSession.objects.create(
            session_key=request.session.session_key,
            program_name=program_name,
            program_display_name=program_config['name'],
            tab_state={}
        )
        
        return JsonResponse({
            'success': True,
            'tab_id': str(tab.tab_id),
            'program_name': program_name,
            'display_name': program_config['name'],
            'url': f"/{program_name.replace('_', '-')}/?tab_id={tab.tab_id}"
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def close_tab(request):
    """Đóng tab"""
    try:
        data = json.loads(request.body)
        tab_id = data.get('tab_id')
        
        if not tab_id:
            return JsonResponse({'error': 'Tab ID required'}, status=400)
        
        # Tìm và đóng tab
        tab = TabSession.objects.filter(
            tab_id=tab_id,
            session_key=request.session.session_key,
            is_active=True
        ).first()
        
        if not tab:
            return JsonResponse({'error': 'Tab not found'}, status=404)
        
        # Đánh dấu tab là không active thay vì xóa
        tab.is_active = False
        tab.save()
        
        # Hủy các task đang chạy của tab này
        TaskStatus.objects.filter(
            tab_id=tab_id,
            status__in=['pending', 'running']
        ).update(status='cancelled')
        
        return JsonResponse({'success': True})
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@require_http_methods(["GET"])
def get_available_programs(request):
    """Lấy danh sách chương trình có thể mở tab mới"""
    session_key = request.session.session_key
    if not session_key:
        # Nếu chưa có session, trả về tất cả chương trình
        available_programs = list(settings.PROGRAM_CONFIGS.keys())
    else:
        # Lấy các chương trình đã mở tab
        opened_programs = set(TabSession.objects.filter(
            session_key=session_key,
            is_active=True
        ).values_list('program_name', flat=True))
        
        # Trả về các chương trình chưa mở
        available_programs = [
            program for program in settings.PROGRAM_CONFIGS.keys()
            if program not in opened_programs
        ]
    
    # Format response
    programs = []
    for program_key in available_programs:
        config = settings.PROGRAM_CONFIGS[program_key]
        programs.append({
            'key': program_key,
            'name': config['name'],
            'description': config['description'],
            'icon': config['icon'],
        })
    
    return JsonResponse({'programs': programs})


@require_http_methods(["GET"])
def get_tab_status(request, tab_id):
    """Lấy trạng thái của tab"""
    try:
        tab = TabSession.objects.filter(
            tab_id=tab_id,
            session_key=request.session.session_key,
            is_active=True
        ).first()
        
        if not tab:
            return JsonResponse({'error': 'Tab not found'}, status=404)
        
        # Lấy task status nếu có
        task_status = TaskStatus.objects.filter(
            tab_id=tab_id,
            status__in=['pending', 'running']
        ).first()
        
        response_data = {
            'tab_id': str(tab.tab_id),
            'program_name': tab.program_name,
            'display_name': tab.program_display_name,
            'tab_state': tab.tab_state,
            'task_status': None
        }
        
        if task_status:
            response_data['task_status'] = {
                'task_id': task_status.task_id,
                'task_name': task_status.task_name,
                'status': task_status.status,
                'progress': task_status.progress,
                'current_step': task_status.current_step,
            }
        
        return JsonResponse(response_data)
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def update_tab_state(request, tab_id):
    """Cập nhật trạng thái tab"""
    try:
        data = json.loads(request.body)
        
        tab = TabSession.objects.filter(
            tab_id=tab_id,
            session_key=request.session.session_key,
            is_active=True
        ).first()
        
        if not tab:
            return JsonResponse({'error': 'Tab not found'}, status=404)
        
        # Cập nhật tab state
        tab.tab_state.update(data.get('state', {}))
        tab.save()
        
        return JsonResponse({'success': True})
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
