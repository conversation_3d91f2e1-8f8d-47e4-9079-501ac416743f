{% extends 'base.html' %}
{% load static %}

{% block title %}Data Scraping - Tiến tr<PERSON>nh xử lý{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/data_scraping.css' %}">
{% endblock %}

{% block content %}
<div class="container-fluid h-100 data-scraping-container">
    <!-- Header -->
    <div class="row bg-primary text-white py-2">
        <div class="col-md-8">
            <h4 class="mb-0">
                <i class="fas fa-search"></i>
                Data Scraping - Tiến trình xử lý
            </h4>
        </div>
        <div class="col-md-4 text-end">
            <button class="btn btn-light btn-sm me-2" id="backBtn">
                <i class="fas fa-arrow-left"></i> Quay lại
            </button>
            <button class="btn btn-danger btn-sm" id="cancelBtn">
                <i class="fas fa-stop"></i> Dừng
            </button>
        </div>
    </div>
    
    <!-- Progress Overview -->
    <div class="row p-3">
        <div class="col-12">
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="mb-0">Tổng quan tiến trình</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="progress-stat">
                                <div class="stat-value" id="completedShops">0</div>
                                <div class="stat-label">Shop đã hoàn thành</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="progress-stat">
                                <div class="stat-value" id="totalProducts">0</div>
                                <div class="stat-label">Tổng sản phẩm</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="progress-stat">
                                <div class="stat-value" id="currentStatus">Đang chuẩn bị</div>
                                <div class="stat-label">Trạng thái</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="progress-stat">
                                <div class="stat-value" id="elapsedTime">00:00</div>
                                <div class="stat-label">Thời gian đã chạy</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div class="mt-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span>Tiến độ xử lý</span>
                            <span id="progressText">0%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="row flex-grow-1 px-3 pb-3">
        <!-- Shop Progress Table -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">Tiến trình từng shop</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive h-100">
                        <table class="table table-striped table-hover mb-0" id="shopProgressTable">
                            <thead class="table-dark sticky-top">
                                <tr>
                                    <th>STT</th>
                                    <th>ID Shop</th>
                                    <th>Trạng thái</th>
                                    <th>Sản phẩm</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Log Messages -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Log chi tiết</h5>
                    <button class="btn btn-sm btn-outline-secondary" id="clearLogsBtn">
                        <i class="fas fa-trash"></i> Xóa log
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="log-container" id="logContainer">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Result Modal -->
<div class="modal fade" id="resultModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Kết quả scraping</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-check-circle text-success fa-5x mb-3"></i>
                    <h4>Hoàn thành thành công!</h4>
                    <p class="lead">Đã scrape <span id="finalProductCount">0</span> sản phẩm từ <span id="finalShopCount">0</span> shop</p>
                    
                    <div class="mt-4">
                        <button class="btn btn-primary btn-lg me-2" id="downloadBtn">
                            <i class="fas fa-download"></i> Tải xuống file Excel
                        </button>
                        <button class="btn btn-secondary" id="newScrapingBtn">
                            <i class="fas fa-plus"></i> Scraping mới
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Error Modal -->
<div class="modal fade" id="errorModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">Lỗi xử lý</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-warning fa-4x mb-3"></i>
                    <h5>Có lỗi xảy ra trong quá trình scraping</h5>
                    <p id="errorMessage" class="text-muted"></p>
                    
                    <div class="mt-4">
                        <button class="btn btn-primary" id="retryBtn">
                            <i class="fas fa-redo"></i> Thử lại
                        </button>
                        <button class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> Đóng
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/data_scraping_step2.js' %}"></script>
<script>
    // Initialize with tab data
    const TAB_ID = '{{ tab_id }}';
    const SESSION_ID = '{{ scraping_session.session_id }}';
    const WEBSOCKET_URL = '{{ request.is_secure|yesno:"wss,ws" }}://{{ request.get_host }}/ws/data-scraping/{{ tab_id }}/';
    
    $(document).ready(function() {
        DataScrapingStep2.init(TAB_ID, SESSION_ID, WEBSOCKET_URL);
    });
</script>
{% endblock %}
