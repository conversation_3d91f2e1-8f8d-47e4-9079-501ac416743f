# Multi-Program WebUI

Hệ thống WebUI đa chương trình được chuyển đổi từ PyQt6 Desktop App sang Django Web Application với hệ thống tab động.

## Tính năng chính

- **Hệ thống Tab động**: Mỗi chương trình chạy trong tab riêng biệt
- **Real-time updates**: WebSocket cho cập nhật trạng thái thời gian thực
- **Background processing**: Celery cho xử lý nền
- **Session management**: Lưu trạng thái tab và tiến trình
- **Responsive design**: Tương thích với mobile và desktop

## Chương trình đã tích hợp

### 1. Data Scraping
- Scrape dữ liệu từ Autoshopee
- Multi-threading với 8 luồng tối ưu
- Anti-bot strategies (User-Agent rotation, delays, session refresh)
- Real-time progress tracking
- Excel export với format tùy chỉnh

## Cấu trúc dự án

```
Django/
├── manage.py
├── requirements.txt
├── setup.py
├── celery_app.py
├── config/                     # Django settings
├── apps/
│   ├── core/                   # Tab management system
│   └── data_scraping/          # Data Scraping program
├── static/                     # CSS, JS, images
├── templates/                  # HTML templates
├── media/                      # File uploads/downloads
└── logs/                       # Application logs
```

## Yêu cầu hệ thống

- Python 3.8+
- Redis Server
- Chrome/Chromium browser (cho Selenium)
- 4GB RAM (khuyến nghị 8GB+)

## Cài đặt

### 1. Clone repository
```bash
git clone <repository-url>
cd Django
```

### 2. Tạo virtual environment
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# hoặc
venv\Scripts\activate     # Windows
```

### 3. Chạy setup script
```bash
python setup.py
```

Script sẽ tự động:
- Cài đặt dependencies
- Tạo database migrations
- Chạy migrations
- Collect static files
- Tạo thư mục cần thiết

### 4. Khởi động Redis
```bash
redis-server
```

### 5. Khởi động Celery worker (terminal mới)
```bash
celery -A celery_app worker --loglevel=info
```

### 6. Khởi động Django server
```bash
python manage.py runserver
```

### 7. Truy cập ứng dụng
Mở browser và truy cập: http://localhost:8000

## Sử dụng

### Giao diện chính
- **Dropdown "Mở chương trình"**: Chọn chương trình để mở tab mới
- **Tab navigation**: Click để chuyển đổi giữa các tab
- **Nút đóng tab**: X trên mỗi tab để đóng

### Data Scraping
1. **Bước 1 - Nhập thông tin**:
   - Nhập danh sách ID shop (mỗi ID một dòng)
   - Cấu hình tên file Excel
   - Thiết lập thông tin đăng nhập Autoshopee

2. **Bước 2 - Theo dõi tiến trình**:
   - Xem tiến độ real-time
   - Theo dõi trạng thái từng shop
   - Xem log chi tiết
   - Tải xuống file kết quả

## Tính năng nâng cao

### WebSocket Real-time Updates
- Cập nhật tiến trình scraping
- Log messages real-time
- Trạng thái tab và task

### Background Processing
- Celery tasks cho scraping
- Queue management
- Task monitoring

### Session Management
- Lưu trạng thái tab
- Restore tiến trình khi reload
- Cross-tab communication

### Anti-bot Strategies
- User-Agent rotation
- Random delays
- Session refresh
- Retry mechanisms

## API Endpoints

### Core APIs
- `GET /` - Tab manager
- `POST /api/tabs/create/` - Tạo tab mới
- `POST /api/tabs/close/` - Đóng tab
- `GET /api/programs/available/` - Danh sách chương trình

### Data Scraping APIs
- `GET /data-scraping/` - Giao diện chính
- `POST /data-scraping/api/start/` - Bắt đầu scraping
- `GET /data-scraping/api/status/<tab_id>/` - Trạng thái session
- `GET /data-scraping/api/download/<tab_id>/` - Tải file kết quả

## WebSocket Endpoints
- `ws://localhost:8000/ws/core/tabs/<tab_id>/` - Tab updates
- `ws://localhost:8000/ws/data-scraping/<tab_id>/` - Scraping updates

## Cấu hình

### Django Settings
Chỉnh sửa `config/settings.py`:
- Database configuration
- Redis configuration
- Static files settings
- Program configurations

### Celery Settings
Chỉnh sửa `celery_app.py`:
- Broker URL
- Task routing
- Beat schedule

### Program Configuration
Thêm chương trình mới trong `config/settings.py`:
```python
PROGRAM_CONFIGS = {
    'new_program': {
        'name': 'New Program',
        'description': 'Description here',
        'icon': '🆕',
        'url_name': 'new_program:index',
    },
}
```

## Thêm chương trình mới

1. **Tạo Django app**:
```bash
python manage.py startapp new_program apps/new_program
```

2. **Thêm vào INSTALLED_APPS** trong settings.py

3. **Tạo models, views, templates**

4. **Thêm WebSocket consumer** (nếu cần real-time)

5. **Cập nhật PROGRAM_CONFIGS**

6. **Tạo migrations và migrate**

## Deployment

### Production Setup
1. **Database**: Sử dụng PostgreSQL
2. **Web Server**: Nginx + Gunicorn
3. **Process Manager**: Supervisor
4. **Caching**: Redis
5. **Static Files**: Nginx serve static files

### Docker Deployment
```bash
# Tạo Dockerfile và docker-compose.yml
docker-compose up -d
```

## Troubleshooting

### Common Issues

1. **Redis connection error**:
   - Kiểm tra Redis server đang chạy
   - Kiểm tra cấu hình CHANNEL_LAYERS

2. **Celery worker không hoạt động**:
   - Kiểm tra Redis connection
   - Restart Celery worker

3. **WebSocket connection failed**:
   - Kiểm tra ASGI configuration
   - Kiểm tra channels và channels-redis

4. **Selenium errors**:
   - Cài đặt Chrome/Chromium
   - Cập nhật ChromeDriver

### Logs
- Django logs: `logs/django.log`
- Celery logs: Console output
- Browser console: F12 Developer Tools

## Contributing

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## License

MIT License - xem file LICENSE để biết chi tiết.

## Support

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra logs
2. Xem phần Troubleshooting
3. Tạo issue trên GitHub
