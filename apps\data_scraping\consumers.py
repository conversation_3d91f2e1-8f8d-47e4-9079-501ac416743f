import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from .models import ScrapingSession, ShopProgress, ScrapingLog


class DataScrapingConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for Data Scraping real-time updates"""
    
    async def connect(self):
        self.tab_id = self.scope['url_route']['kwargs']['tab_id']
        self.group_name = f'tab_{self.tab_id}'
        
        # Join group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Send initial status
        status = await self.get_session_status()
        if status:
            await self.send(text_data=json.dumps({
                'type': 'session_status',
                'data': status
            }))
    
    async def disconnect(self, close_code):
        # Leave group
        await self.channel_layer.group_discard(
            self.group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        """Handle messages from WebSocket"""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong'
                }))
            elif message_type == 'get_status':
                status = await self.get_session_status()
                await self.send(text_data=json.dumps({
                    'type': 'session_status',
                    'data': status
                }))
            elif message_type == 'get_logs':
                logs = await self.get_recent_logs()
                await self.send(text_data=json.dumps({
                    'type': 'logs_update',
                    'data': logs
                }))
                
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON'
            }))
    
    # Group message handlers
    async def task_update(self, event):
        """Handle task update messages"""
        await self.send(text_data=json.dumps({
            'type': 'task_update',
            'data': event['data']
        }))
    
    async def log_message(self, event):
        """Handle log messages"""
        await self.send(text_data=json.dumps({
            'type': 'log_message',
            'data': event['data']
        }))
    
    async def shop_status_update(self, event):
        """Handle shop status updates"""
        await self.send(text_data=json.dumps({
            'type': 'shop_status_update',
            'data': event['data']
        }))
    
    @database_sync_to_async
    def get_session_status(self):
        """Get current session status"""
        try:
            session = ScrapingSession.objects.filter(tab_id=self.tab_id).first()
            if not session:
                return None
            
            # Get shop progress
            shop_progress = list(ShopProgress.objects.filter(
                session=session
            ).values('shop_id', 'status', 'product_count'))
            
            # Calculate progress
            progress_percentage = 0
            if session.total_shops > 0:
                progress_percentage = int((session.completed_shops / session.total_shops) * 100)
            
            return {
                'session_id': str(session.session_id),
                'status': session.status,
                'current_step': session.current_step,
                'total_shops': session.total_shops,
                'completed_shops': session.completed_shops,
                'total_products': session.total_products,
                'progress_percentage': progress_percentage,
                'shop_progress': shop_progress,
                'result_file_path': session.result_file_path,
                'error_message': session.error_message,
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    @database_sync_to_async
    def get_recent_logs(self):
        """Get recent log messages"""
        try:
            session = ScrapingSession.objects.filter(tab_id=self.tab_id).first()
            if not session:
                return []
            
            logs = list(ScrapingLog.objects.filter(
                session=session
            ).order_by('-created_at')[:100].values(
                'level', 'message', 'shop_id', 'created_at'
            ))
            
            # Convert datetime to string
            for log in logs:
                log['created_at'] = log['created_at'].isoformat()
            
            return logs
            
        except Exception as e:
            return [{'error': str(e)}]
