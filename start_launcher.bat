@echo off
title Multi-Program WebUI Launcher
echo.
echo ========================================
echo   Multi-Program WebUI Launcher
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "launcher.py" (
    echo ERROR: launcher.py not found
    echo Please run this script from the project directory
    pause
    exit /b 1
)

echo Starting Multi-Program WebUI Launcher...
echo.

REM Run the launcher
python launcher.py

REM If launcher exits, pause to see any error messages
if errorlevel 1 (
    echo.
    echo Launcher exited with error. Check the message above.
    pause
)
