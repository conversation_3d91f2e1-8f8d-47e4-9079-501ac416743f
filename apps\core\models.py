from django.db import models
from django.contrib.auth.models import User
import uuid


class TabSession(models.Model):
    """Model để lưu trạng thái các tab đang mở"""
    session_key = models.CharField(max_length=40, db_index=True)
    tab_id = models.UUIDField(default=uuid.uuid4, unique=True)
    program_name = models.CharField(max_length=50)
    program_display_name = models.CharField(max_length=100)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Store tab state as JSON
    tab_state = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'core_tab_sessions'
        indexes = [
            models.Index(fields=['session_key', 'is_active']),
            models.Index(fields=['program_name']),
        ]
    
    def __str__(self):
        return f"{self.program_display_name} - {self.tab_id}"


class ProgramConfig(models.Model):
    """Model để lưu cấu hình các chương trình"""
    name = models.Char<PERSON><PERSON>(max_length=50, unique=True)
    display_name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=10, default='📋')
    url_name = models.CharField(max_length=100)
    is_enabled = models.BooleanField(default=True)
    order = models.IntegerField(default=0)
    
    class Meta:
        db_table = 'core_program_configs'
        ordering = ['order', 'display_name']
    
    def __str__(self):
        return self.display_name


class TaskStatus(models.Model):
    """Model để theo dõi trạng thái các task background"""
    task_id = models.CharField(max_length=255, unique=True)
    tab_id = models.UUIDField()
    program_name = models.CharField(max_length=50)
    task_name = models.CharField(max_length=100)
    status = models.CharField(max_length=20, choices=[
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ], default='pending')
    
    progress = models.IntegerField(default=0)  # 0-100
    current_step = models.CharField(max_length=200, blank=True)
    result_data = models.JSONField(default=dict, blank=True)
    error_message = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'core_task_status'
        indexes = [
            models.Index(fields=['tab_id', 'status']),
            models.Index(fields=['task_id']),
        ]
    
    def __str__(self):
        return f"{self.task_name} - {self.status}"
