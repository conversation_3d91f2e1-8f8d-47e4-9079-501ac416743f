/* Tab system styles */

.nav-tabs {
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
    padding: 0.5rem 1rem 0;
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
    color: #495057;
    background-color: transparent;
    padding: 0.75rem 1rem;
    margin-right: 0.25rem;
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 150px;
    max-width: 250px;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    background-color: #e9ecef;
    color: #495057;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    border-bottom: 2px solid #fff;
    margin-bottom: -2px;
}

.nav-tabs .nav-link .tab-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.nav-tabs .nav-link .tab-close {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 1.2rem;
    line-height: 1;
    padding: 0;
    margin-left: 0.5rem;
    cursor: pointer;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.nav-tabs .nav-link .tab-close:hover {
    background-color: #dc3545;
    color: white;
}

.nav-tabs .nav-link .tab-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.25rem;
    flex-shrink: 0;
}

.tab-status.running {
    background-color: #28a745;
    animation: pulse 1.5s infinite;
}

.tab-status.idle {
    background-color: #6c757d;
}

.tab-status.error {
    background-color: #dc3545;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Tab content */
.tab-content {
    background-color: #fff;
    border-left: 2px solid #dee2e6;
    border-right: 2px solid #dee2e6;
    border-bottom: 2px solid #dee2e6;
    border-bottom-left-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.tab-pane {
    padding: 0;
    height: 100%;
}

.tab-pane iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 0 0 0.375rem 0.375rem;
}

/* Welcome screen */
.program-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
    height: 100%;
}

.program-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.program-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.open-program-btn {
    transition: all 0.2s ease;
}

.open-program-btn:hover {
    transform: scale(1.05);
}

/* Dropdown styles */
.dropdown-menu {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item .program-icon {
    font-size: 1.5rem;
    margin-bottom: 0;
    width: 2rem;
    text-align: center;
}

.dropdown-item .program-info {
    flex: 1;
}

.dropdown-item .program-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.dropdown-item .program-description {
    font-size: 0.875rem;
    color: #6c757d;
    margin: 0;
}

/* Loading states */
.tab-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    flex-direction: column;
    gap: 1rem;
}

.tab-loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .nav-tabs {
        padding: 0.25rem 0.5rem 0;
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .nav-tabs .nav-link {
        min-width: 120px;
        max-width: 180px;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .program-card {
        margin-bottom: 1rem;
    }
    
    .program-icon {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .nav-tabs .nav-link {
        min-width: 100px;
        max-width: 150px;
        padding: 0.5rem;
    }
    
    .nav-tabs .nav-link .tab-title {
        font-size: 0.8rem;
    }
    
    .dropdown-item {
        padding: 0.5rem 0.75rem;
    }
    
    .dropdown-item .program-icon {
        font-size: 1.25rem;
        width: 1.5rem;
    }
}

/* Animation for tab transitions */
.tab-pane {
    transition: opacity 0.3s ease;
}

.tab-pane.fade {
    opacity: 0;
}

.tab-pane.fade.show {
    opacity: 1;
}

/* Custom scrollbar for tab navigation */
.nav-tabs::-webkit-scrollbar {
    height: 4px;
}

.nav-tabs::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.nav-tabs::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 2px;
}

.nav-tabs::-webkit-scrollbar-thumb:hover {
    background: #555;
}
