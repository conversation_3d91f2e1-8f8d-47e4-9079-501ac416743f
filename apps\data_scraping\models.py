from django.db import models
import uuid
import json


class ScrapingSession(models.Model):
    """Model để lưu session scraping"""
    session_id = models.UUIDField(default=uuid.uuid4, unique=True)
    tab_id = models.UUIDField()
    
    # Input parameters
    shop_ids = models.JSONField(default=list)
    output_path = models.CharField(max_length=500)
    output_filename = models.CharField(max_length=255)
    
    # Authentication info (encrypted)
    username = models.Char<PERSON>ield(max_length=100)
    password = models.CharField(max_length=100)  # Should be encrypted in production
    
    # Status tracking
    status = models.CharField(max_length=20, choices=[
        ('idle', 'Idle'),
        ('running', 'Running'),
        ('paused', 'Paused'),
        ('completed', 'Completed'),
        ('error', 'Error'),
        ('cancelled', 'Cancelled'),
    ], default='idle')
    
    current_step = models.IntegerField(default=1)  # 1=input, 2=processing
    
    # Progress tracking
    total_shops = models.IntegerField(default=0)
    completed_shops = models.IntegerField(default=0)
    total_products = models.IntegerField(default=0)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Results
    result_file_path = models.CharField(max_length=500, blank=True)
    error_message = models.TextField(blank=True)
    
    class Meta:
        db_table = 'data_scraping_sessions'
        indexes = [
            models.Index(fields=['tab_id', 'status']),
            models.Index(fields=['session_id']),
        ]
    
    def __str__(self):
        return f"Scraping Session {self.session_id} - {self.status}"


class ShopProgress(models.Model):
    """Model để theo dõi tiến trình từng shop"""
    session = models.ForeignKey(ScrapingSession, on_delete=models.CASCADE, related_name='shop_progress')
    shop_id = models.CharField(max_length=50)
    
    status = models.CharField(max_length=20, choices=[
        ('waiting', 'Waiting'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('error', 'Error'),
        ('no_products', 'No Products'),
    ], default='waiting')
    
    product_count = models.IntegerField(default=0)
    error_message = models.TextField(blank=True)
    
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'data_scraping_shop_progress'
        unique_together = ['session', 'shop_id']
        indexes = [
            models.Index(fields=['session', 'status']),
        ]
    
    def __str__(self):
        return f"Shop {self.shop_id} - {self.status}"


class ScrapingLog(models.Model):
    """Model để lưu log scraping"""
    session = models.ForeignKey(ScrapingSession, on_delete=models.CASCADE, related_name='logs')
    
    level = models.CharField(max_length=10, choices=[
        ('DEBUG', 'Debug'),
        ('INFO', 'Info'),
        ('WARNING', 'Warning'),
        ('ERROR', 'Error'),
    ], default='INFO')
    
    message = models.TextField()
    shop_id = models.CharField(max_length=50, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'data_scraping_logs'
        indexes = [
            models.Index(fields=['session', 'created_at']),
            models.Index(fields=['level']),
        ]
    
    def __str__(self):
        return f"{self.level}: {self.message[:50]}"


class LoginCredentials(models.Model):
    """Model để lưu thông tin đăng nhập (encrypted)"""
    tab_id = models.UUIDField(unique=True)
    username = models.CharField(max_length=100)
    password = models.CharField(max_length=100)  # Should be encrypted
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'data_scraping_credentials'
    
    def __str__(self):
        return f"Credentials for {self.username}"
