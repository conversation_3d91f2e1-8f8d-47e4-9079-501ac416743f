/**
 * Data Scraping Step 1 - Input Form
 */

class DataScrapingStep1 {
    constructor() {
        this.tabId = null;
        this.credentials = {
            username: 'prince<PERSON><PERSON>',
            password: 'Beyondk@2025'
        };
    }

    init(tabId, initialCredentials = {}) {
        this.tabId = tabId;
        this.credentials = { ...this.credentials, ...initialCredentials };
        
        this.setupEventListeners();
        this.loadCredentials();
    }

    setupEventListeners() {
        // Form submission
        $('#scrapingForm').on('submit', (e) => {
            e.preventDefault();
            this.startProcessing();
        });

        // Browse button (placeholder - would need file dialog in real implementation)
        $('#browseBtn').on('click', () => {
            this.showAlert('Chức năng browse folder sẽ được implement sau. Hiện tại sử dụng đường dẫn mặc định.', 'info');
        });

        // Login button
        $('#loginBtn').on('click', () => {
            this.showLoginModal();
        });

        // Save login credentials
        $('#saveLoginBtn').on('click', () => {
            this.saveCredentials();
        });

        // Paste from clipboard
        $('#pasteBtn').on('click', () => {
            this.pasteFromClipboard();
        });

        // Clear all
        $('#clearBtn').on('click', () => {
            $('#shopIds').val('');
        });

        // Auto-format shop IDs on paste
        $('#shopIds').on('paste', (e) => {
            setTimeout(() => {
                this.formatShopIds();
            }, 100);
        });
    }

    async loadCredentials() {
        try {
            const response = await fetch(`/data-scraping/api/credentials/${this.tabId}/`);
            const data = await response.json();
            
            if (data.username && data.password) {
                this.credentials = data;
            }
        } catch (error) {
            console.error('Error loading credentials:', error);
        }
    }

    showLoginModal() {
        $('#username').val(this.credentials.username);
        $('#password').val(this.credentials.password);
        $('#loginModal').modal('show');
    }

    async saveCredentials() {
        const username = $('#username').val().trim();
        const password = $('#password').val().trim();

        if (!username || !password) {
            this.showAlert('Vui lòng nhập đầy đủ thông tin đăng nhập!', 'warning');
            return;
        }

        try {
            const response = await fetch('/data-scraping/api/credentials/save/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': window.CSRF_TOKEN
                },
                body: JSON.stringify({
                    tab_id: this.tabId,
                    username: username,
                    password: password
                })
            });

            const data = await response.json();

            if (data.success) {
                this.credentials = { username, password };
                $('#loginModal').modal('hide');
                this.showAlert('Đã lưu thông tin đăng nhập thành công!', 'success');
            } else {
                throw new Error(data.error || 'Failed to save credentials');
            }
        } catch (error) {
            console.error('Error saving credentials:', error);
            this.showAlert('Lỗi khi lưu thông tin đăng nhập: ' + error.message, 'danger');
        }
    }

    async pasteFromClipboard() {
        try {
            const text = await navigator.clipboard.readText();
            $('#shopIds').val(text);
            this.formatShopIds();
            this.showAlert('Đã dán dữ liệu từ clipboard!', 'success');
        } catch (error) {
            console.error('Error reading clipboard:', error);
            this.showAlert('Không thể đọc clipboard. Vui lòng dán thủ công (Ctrl+V).', 'warning');
        }
    }

    formatShopIds() {
        const text = $('#shopIds').val();
        if (!text.trim()) return;

        // Split by lines and clean up
        const lines = text.split('\n').map(line => line.trim()).filter(line => line);
        
        // Remove duplicates and extract IDs
        const uniqueIds = [];
        const seen = new Set();

        lines.forEach(line => {
            // Remove existing numbering
            line = line.replace(/^\d+\.\s*/, '');
            
            // Extract numeric ID
            const match = line.match(/\d+/);
            if (match) {
                const id = match[0];
                if (!seen.has(id)) {
                    seen.add(id);
                    uniqueIds.push(id);
                }
            }
        });

        // Format with numbering
        const formatted = uniqueIds.map((id, index) => `${index + 1}. ${id}`).join('\n');
        $('#shopIds').val(formatted);
    }

    validateForm() {
        const shopIds = $('#shopIds').val().trim();
        const outputFilename = $('#outputFilename').val().trim();

        if (!shopIds) {
            this.showAlert('Vui lòng nhập danh sách ID shop!', 'warning');
            return false;
        }

        if (!outputFilename) {
            this.showAlert('Vui lòng nhập tên file Excel!', 'warning');
            return false;
        }

        // Parse shop IDs
        const parsedIds = this.parseShopIds(shopIds);
        if (parsedIds.length === 0) {
            this.showAlert('Không tìm thấy ID shop hợp lệ!', 'warning');
            return false;
        }

        return true;
    }

    parseShopIds(text) {
        const lines = text.split('\n').map(line => line.trim()).filter(line => line);
        const shopIds = [];

        lines.forEach(line => {
            // Remove numbering
            line = line.replace(/^\d+\.\s*/, '');
            
            // Extract numeric ID
            const match = line.match(/\d+/);
            if (match) {
                shopIds.push(match[0]);
            }
        });

        return shopIds;
    }

    async startProcessing() {
        if (!this.validateForm()) {
            return;
        }

        const shopIds = $('#shopIds').val().trim();
        const outputPath = $('#outputPath').val().trim();
        const outputFilename = $('#outputFilename').val().trim();

        // Show loading modal
        $('#loadingModal').modal('show');

        try {
            const response = await fetch('/data-scraping/api/start/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': window.CSRF_TOKEN
                },
                body: JSON.stringify({
                    tab_id: this.tabId,
                    shop_ids: shopIds,
                    output_path: outputPath,
                    output_filename: outputFilename,
                    username: this.credentials.username,
                    password: this.credentials.password
                })
            });

            const data = await response.json();

            if (data.success) {
                // Redirect to step 2
                window.location.href = data.redirect_url;
            } else {
                throw new Error(data.error || 'Failed to start processing');
            }
        } catch (error) {
            console.error('Error starting processing:', error);
            this.showAlert('Lỗi khi bắt đầu xử lý: ' + error.message, 'danger');
        } finally {
            $('#loadingModal').modal('hide');
        }
    }

    showAlert(message, type = 'info') {
        // Remove existing alerts
        $('.alert').remove();

        const alertClass = `alert-${type}`;
        const alert = $(`
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);

        // Add to top of container
        $('.data-scraping-container').prepend(alert);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            alert.alert('close');
        }, 5000);
    }
}

// Global instance
window.DataScrapingStep1 = new DataScrapingStep1();
