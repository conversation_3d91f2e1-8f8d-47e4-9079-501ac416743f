#!/usr/bin/env python3
"""
Create desktop shortcut for Multi-Program WebUI Launcher
"""

import os
import sys
from pathlib import Path

def create_windows_shortcut():
    """Create Windows desktop shortcut"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "Multi-Program WebUI.lnk")
        target = str(Path(__file__).parent / "start_launcher.bat")
        wDir = str(Path(__file__).parent)
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = target
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.Description = "Multi-Program WebUI Launcher"
        shortcut.save()
        
        print(f"✅ Desktop shortcut created: {path}")
        return True
        
    except ImportError:
        print("⚠️ winshell not available. Installing...")
        try:
            import subprocess
            subprocess.run([sys.executable, "-m", "pip", "install", "pywin32", "winshell"], check=True)
            print("✅ Dependencies installed. Please run this script again.")
            return False
        except:
            print("❌ Failed to install dependencies")
            return False
    except Exception as e:
        print(f"❌ Error creating shortcut: {e}")
        return False

def create_linux_shortcut():
    """Create Linux desktop shortcut"""
    try:
        desktop_dir = Path.home() / "Desktop"
        if not desktop_dir.exists():
            desktop_dir = Path.home() / ".local" / "share" / "applications"
            desktop_dir.mkdir(parents=True, exist_ok=True)
        
        shortcut_path = desktop_dir / "multi-program-webui.desktop"
        launcher_path = Path(__file__).parent / "start_launcher.sh"
        icon_path = Path(__file__).parent / "launcher.py"  # Use launcher.py as icon for now
        
        shortcut_content = f"""[Desktop Entry]
Version=1.0
Type=Application
Name=Multi-Program WebUI
Comment=One-click solution to start all services
Exec={launcher_path}
Icon={icon_path}
Terminal=false
StartupNotify=true
Categories=Development;
"""
        
        with open(shortcut_path, 'w') as f:
            f.write(shortcut_content)
        
        # Make executable
        os.chmod(shortcut_path, 0o755)
        os.chmod(launcher_path, 0o755)
        
        print(f"✅ Desktop shortcut created: {shortcut_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating shortcut: {e}")
        return False

def create_mac_shortcut():
    """Create macOS application bundle"""
    try:
        app_dir = Path.home() / "Applications" / "Multi-Program WebUI.app"
        contents_dir = app_dir / "Contents"
        macos_dir = contents_dir / "MacOS"
        resources_dir = contents_dir / "Resources"
        
        # Create directories
        macos_dir.mkdir(parents=True, exist_ok=True)
        resources_dir.mkdir(parents=True, exist_ok=True)
        
        # Create Info.plist
        info_plist = contents_dir / "Info.plist"
        plist_content = """<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>launcher</string>
    <key>CFBundleIdentifier</key>
    <string>com.multiprogram.webui</string>
    <key>CFBundleName</key>
    <string>Multi-Program WebUI</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
</dict>
</plist>"""
        
        with open(info_plist, 'w') as f:
            f.write(plist_content)
        
        # Create launcher script
        launcher_script = macos_dir / "launcher"
        script_content = f"""#!/bin/bash
cd "{Path(__file__).parent}"
python3 launcher.py
"""
        
        with open(launcher_script, 'w') as f:
            f.write(script_content)
        
        # Make executable
        os.chmod(launcher_script, 0o755)
        
        print(f"✅ Application bundle created: {app_dir}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating application bundle: {e}")
        return False

def main():
    """Main function"""
    print("🔗 Creating desktop shortcut for Multi-Program WebUI Launcher...")
    print()
    
    if sys.platform == "win32":
        success = create_windows_shortcut()
    elif sys.platform == "darwin":
        success = create_mac_shortcut()
    else:
        success = create_linux_shortcut()
    
    if success:
        print()
        print("🎉 Shortcut created successfully!")
        print("You can now launch the application from your desktop.")
    else:
        print()
        print("❌ Failed to create shortcut.")
        print("You can still run the launcher manually:")
        if sys.platform == "win32":
            print("  Double-click: start_launcher.bat")
        else:
            print("  Run: ./start_launcher.sh")

if __name__ == "__main__":
    main()
