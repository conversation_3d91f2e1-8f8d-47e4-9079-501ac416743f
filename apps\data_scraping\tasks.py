"""
Celery tasks for Data Scraping background processing
"""

from celery import shared_task
from django.utils import timezone
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import os
import time
import requests
import concurrent.futures
from .models import ScrapingSession, ShopProgress, ScrapingLog
from .scraping_core import (
    get_logged_in_cookies, 
    get_auth_headers, 
    fetch_shop_products,
    ScrapingProgressCallback,
    OPTIMAL_THREADS
)
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


@shared_task(bind=True)
def start_scraping_task(self, session_id, shop_ids, username, password, output_path, output_filename):
    """
    Main Celery task để thực hiện scraping
    """
    try:
        # Get session
        session = ScrapingSession.objects.get(session_id=session_id)
        session.status = 'running'
        session.started_at = timezone.now()
        session.total_shops = len(shop_ids)
        session.save()
        
        # Setup progress callback
        websocket_group_name = f'tab_{session.tab_id}'
        progress_callback = ScrapingProgressCallback(session_id, websocket_group_name)
        
        # Initialize shop progress records
        for shop_id in shop_ids:
            ShopProgress.objects.get_or_create(
                session=session,
                shop_id=shop_id,
                defaults={'status': 'waiting'}
            )
        
        progress_callback.log("🚀 Bắt đầu quá trình xử lý dữ liệu...")
        
        # Login and get authentication
        progress_callback.log("🔑 Đang đăng nhập vào Autoshopee...")
        get_logged_in_cookies(username, password, headless=True)
        headers = get_auth_headers()
        
        if not headers:
            raise Exception("❌ Không thể lấy thông tin xác thực!")
        
        progress_callback.log("✅ Đăng nhập thành công.")
        
        # Ensure output directory exists
        if not os.path.exists(output_path):
            os.makedirs(output_path)
        
        # Create full output path
        full_path = os.path.join(output_path, output_filename)
        if not full_path.endswith('.xlsx'):
            full_path += '.xlsx'
        
        # Initialize Excel writer
        excel_writer = DjangoStreamingExcelWriter(full_path, progress_callback)
        
        # Create session for requests
        requests_session = requests.Session()
        shop_count = 0
        total_products = 0
        
        # Use ThreadPoolExecutor for concurrent processing
        with concurrent.futures.ThreadPoolExecutor(max_workers=OPTIMAL_THREADS) as executor:
            # Submit all shop tasks
            futures = {
                executor.submit(
                    fetch_shop_products,
                    shop_id,
                    requests_session,
                    headers,
                    30,  # timeout
                    3,   # max_retries
                    progress_callback
                ): shop_id for shop_id in shop_ids
            }
            
            # Process completed tasks
            for i, future in enumerate(concurrent.futures.as_completed(futures)):
                shop_id = futures[future]
                try:
                    shop_products = future.result()
                    if shop_products:
                        # Write products to Excel immediately
                        excel_writer.append_products(shop_products)
                        total_products += len(shop_products)
                        progress_callback.log(f"📊 Đã ghi {len(shop_products)} sản phẩm từ shop {shop_id} vào Excel")
                    
                    shop_count += 1
                    
                    # Update progress
                    progress_callback.update_progress(shop_count, len(shop_ids), total_products)
                    progress_callback.log(f"🔄 Tiến độ: {shop_count}/{len(shop_ids)} shop đã hoàn thành")
                    
                    # Session refresh every 100 shops
                    if shop_count % 100 == 0:
                        progress_callback.log(f"🔄 Refresh session sau {shop_count} shop để tránh detection...")
                        requests_session.close()
                        requests_session = requests.Session()
                        time.sleep(10)  # Wait after refresh
                    
                    # Delay between shops
                    elif i < len(shop_ids) - 1:
                        time.sleep(8)  # 8 second delay
                        
                except Exception as e:
                    progress_callback.log(f"❌ Lỗi khi xử lý shop ID {shop_id}: {str(e)}", level='ERROR')
                    # Update shop status to error
                    progress_callback.update_shop_status(shop_id, 'error')
        
        # Finalize Excel file
        excel_writer.finalize()
        
        # Update session status
        session.status = 'completed'
        session.completed_at = timezone.now()
        session.result_file_path = full_path
        session.total_products = total_products
        session.completed_shops = shop_count
        session.save()
        
        progress_callback.log(f"✅ Hoàn thành! Đã lưu {total_products} sản phẩm vào {output_filename}")
        
        # Send completion notification via WebSocket
        channel_layer = get_channel_layer()
        async_to_sync(channel_layer.group_send)(
            websocket_group_name,
            {
                'type': 'task_update',
                'data': {
                    'status': 'completed',
                    'total_products': total_products,
                    'result_file': output_filename,
                    'download_url': f'/media/exports/{os.path.basename(full_path)}'
                }
            }
        )
        
        return {
            'success': True,
            'total_products': total_products,
            'result_file': full_path
        }
        
    except Exception as e:
        logger.error(f"Scraping task failed: {e}")
        
        # Update session status
        try:
            session = ScrapingSession.objects.get(session_id=session_id)
            session.status = 'error'
            session.error_message = str(e)
            session.save()
        except:
            pass
        
        # Send error notification
        try:
            websocket_group_name = f'tab_{session.tab_id}'
            channel_layer = get_channel_layer()
            async_to_sync(channel_layer.group_send)(
                websocket_group_name,
                {
                    'type': 'task_update',
                    'data': {
                        'status': 'error',
                        'error_message': str(e)
                    }
                }
            )
        except:
            pass
        
        raise


@shared_task
def cleanup_old_sessions():
    """
    Cleanup old scraping sessions and files
    """
    from datetime import timedelta
    cutoff_date = timezone.now() - timedelta(days=7)
    
    # Delete old sessions
    old_sessions = ScrapingSession.objects.filter(
        created_at__lt=cutoff_date,
        status__in=['completed', 'error', 'cancelled']
    )
    
    # Delete associated files
    for session in old_sessions:
        if session.result_file_path and os.path.exists(session.result_file_path):
            try:
                os.remove(session.result_file_path)
            except:
                pass
    
    # Delete from database
    deleted_count = old_sessions.count()
    old_sessions.delete()
    
    logger.info(f"Cleaned up {deleted_count} old scraping sessions")
    return deleted_count


class DjangoStreamingExcelWriter:
    """
    Excel writer adapted for Django environment
    """
    def __init__(self, filename, progress_callback=None):
        from openpyxl import Workbook
        
        self.filename = filename
        self.progress_callback = progress_callback
        self.wb = Workbook()
        self.ws = self.wb.active
        self.current_row = 1
        self.headers_written = False
    
    def log(self, message):
        if self.progress_callback:
            self.progress_callback.log(message)
        else:
            logger.info(message)
    
    def append_products(self, products):
        """Add products to Excel"""
        if not products:
            return
        
        try:
            import pandas as pd
            from .scraping_core import clean_text_for_excel
            
            # Clean data
            clean_products = []
            for product in products:
                clean_product = {}
                for key, value in product.items():
                    if isinstance(value, str):
                        clean_product[key] = clean_text_for_excel(value)
                    else:
                        clean_product[key] = value
                clean_products.append(clean_product)
            
            # Create DataFrame
            df = pd.json_normalize(clean_products)
            
            # Format DataFrame
            new_df = self._format_dataframe(df)
            
            # Write headers if not written
            if not self.headers_written:
                self._write_headers(new_df)
                self.headers_written = True
            
            # Write data
            self._write_data(new_df)
            
        except Exception as e:
            self.log(f"❌ Lỗi khi ghi dữ liệu vào Excel: {str(e)}")
    
    def _format_dataframe(self, df):
        """Format DataFrame according to requirements"""
        import pandas as pd
        
        # Create key columns
        key_col = df['id'].astype(str) + '_' + df['shopID'].astype(str)
        sku_col = df['id']
        id_col = df['id']
        
        # Create new DataFrame
        new_df = pd.DataFrame({
            '': key_col,
            ' ': sku_col,
            '  ': id_col
        })
        
        # Add other columns
        column_mapping = {
            'linkProduct': 'Link sản phẩm',
            'linkShop': 'Link Shop',
            'name': 'Tên sản phẩm',
            'brand': 'Thương hiệu',
            'description': 'Mô tả',
            'timeCreate': 'Ngày tạo',
            'itemID': 'Mã Shop',
            'shopID': 'Mã Sản phẩm',
            'categoryMain': 'Chuyên mục',
            'price': 'Giá',
            'priceMin': 'Giá min',
            'priceMax': 'Giá max',
            'discount': 'Giảm giá',
            'stock': 'Kho',
            'likedCount': 'Số thích',
            'itemRating': 'Điểm đánh giá',
            'sold_30day': 'Đã bán 30 ngày',
            'sale_30day': 'Doanh số 30 ngày',
            'sold_alltime': 'Đã bán toàn thời gian',
            'sale_alltime': 'Doanh số toàn thời gian',
            'location': 'Vị trí',
            'video': 'Video'
        }
        
        for field_name, excel_header in column_mapping.items():
            new_df[excel_header] = df[field_name] if field_name in df.columns else ''
        
        return new_df
    
    def _write_headers(self, df):
        """Write headers to Excel"""
        from openpyxl.styles import Font
        
        for c_idx, column in enumerate(df.columns, 1):
            if c_idx <= 3:
                header_value = ""
            else:
                header_value = column
            
            self.ws.cell(row=self.current_row, column=c_idx, value=header_value)
            self.ws.cell(row=self.current_row, column=c_idx).font = Font(bold=False)
        
        self.current_row += 1
    
    def _write_data(self, df):
        """Write data to Excel"""
        from .scraping_core import clean_text_for_excel
        
        for _, row in df.iterrows():
            for c_idx, value in enumerate(row, 1):
                try:
                    if isinstance(value, str):
                        value = clean_text_for_excel(value)
                    self.ws.cell(row=self.current_row, column=c_idx, value=value)
                except Exception as cell_error:
                    self.log(f"⚠️ Lỗi khi ghi cell ({self.current_row}, {c_idx}): {str(cell_error)}")
                    self.ws.cell(row=self.current_row, column=c_idx, value="[Lỗi dữ liệu]")
            
            self.current_row += 1
    
    def finalize(self):
        """Save Excel file"""
        try:
            self.wb.save(self.filename)
            self.log(f"📁 Đã lưu file Excel: {self.filename}")
        except Exception as e:
            self.log(f"❌ Lỗi khi lưu file Excel: {str(e)}")
