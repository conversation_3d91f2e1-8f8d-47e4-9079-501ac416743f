from django.urls import path
from . import views

app_name = 'data_scraping'

urlpatterns = [
    path('', views.index, name='index'),
    path('api/start/', views.start_processing, name='start_processing'),
    path('api/status/<uuid:tab_id>/', views.get_session_status, name='session_status'),
    path('api/credentials/save/', views.save_login_credentials, name='save_credentials'),
    path('api/credentials/<uuid:tab_id>/', views.get_login_credentials, name='get_credentials'),
    path('api/download/<uuid:tab_id>/', views.download_result, name='download_result'),
    path('api/cancel/', views.cancel_scraping, name='cancel_scraping'),
]
