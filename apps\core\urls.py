from django.urls import path
from . import views

app_name = 'core'

urlpatterns = [
    path('', views.index, name='index'),
    path('api/tabs/create/', views.create_tab, name='create_tab'),
    path('api/tabs/close/', views.close_tab, name='close_tab'),
    path('api/programs/available/', views.get_available_programs, name='available_programs'),
    path('api/tabs/<uuid:tab_id>/status/', views.get_tab_status, name='tab_status'),
    path('api/tabs/<uuid:tab_id>/state/', views.update_tab_state, name='update_tab_state'),
]
