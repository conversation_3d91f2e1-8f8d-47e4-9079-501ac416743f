/**
 * WebSocket Handler for real-time communication
 */

class WebSocketHandler {
    constructor(url) {
        this.url = url;
        this.socket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 1000; // Start with 1 second
        this.messageHandlers = new Map();
        this.isConnected = false;
        this.shouldReconnect = true;
    }

    connect() {
        try {
            this.socket = new WebSocket(this.url);
            this.setupEventHandlers();
        } catch (error) {
            console.error('WebSocket connection error:', error);
            this.handleReconnect();
        }
    }

    setupEventHandlers() {
        this.socket.onopen = (event) => {
            console.log('WebSocket connected:', this.url);
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.reconnectInterval = 1000;
            this.onOpen(event);
        };

        this.socket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };

        this.socket.onclose = (event) => {
            console.log('WebSocket disconnected:', event.code, event.reason);
            this.isConnected = false;
            this.onClose(event);
            
            if (this.shouldReconnect && event.code !== 1000) {
                this.handleReconnect();
            }
        };

        this.socket.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.onError(error);
        };
    }

    handleMessage(data) {
        const messageType = data.type;
        
        // Call specific handlers
        if (this.messageHandlers.has(messageType)) {
            const handlers = this.messageHandlers.get(messageType);
            handlers.forEach(handler => {
                try {
                    handler(data.data || data);
                } catch (error) {
                    console.error(`Error in message handler for ${messageType}:`, error);
                }
            });
        }

        // Call general message handler
        this.onMessage(data);
    }

    handleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            this.shouldReconnect = false;
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);
        
        console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
        
        setTimeout(() => {
            if (this.shouldReconnect) {
                this.connect();
            }
        }, delay);
    }

    send(data) {
        if (this.isConnected && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify(data));
            return true;
        } else {
            console.warn('WebSocket not connected, message not sent:', data);
            return false;
        }
    }

    addMessageHandler(messageType, handler) {
        if (!this.messageHandlers.has(messageType)) {
            this.messageHandlers.set(messageType, []);
        }
        this.messageHandlers.get(messageType).push(handler);
    }

    removeMessageHandler(messageType, handler) {
        if (this.messageHandlers.has(messageType)) {
            const handlers = this.messageHandlers.get(messageType);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    disconnect() {
        this.shouldReconnect = false;
        if (this.socket) {
            this.socket.close(1000, 'Manual disconnect');
        }
    }

    // Override these methods in subclasses or set as properties
    onOpen(event) {
        // Override in subclass
    }

    onClose(event) {
        // Override in subclass
    }

    onError(error) {
        // Override in subclass
    }

    onMessage(data) {
        // Override in subclass
    }

    // Utility methods
    ping() {
        this.send({ type: 'ping' });
    }

    getStatus() {
        this.send({ type: 'get_status' });
    }
}

// Global WebSocket manager
window.WebSocketManager = {
    connections: new Map(),

    create(name, url) {
        if (this.connections.has(name)) {
            this.connections.get(name).disconnect();
        }
        
        const ws = new WebSocketHandler(url);
        this.connections.set(name, ws);
        return ws;
    },

    get(name) {
        return this.connections.get(name);
    },

    disconnect(name) {
        if (this.connections.has(name)) {
            this.connections.get(name).disconnect();
            this.connections.delete(name);
        }
    },

    disconnectAll() {
        this.connections.forEach((ws, name) => {
            ws.disconnect();
        });
        this.connections.clear();
    }
};

// Auto-cleanup on page unload
window.addEventListener('beforeunload', () => {
    window.WebSocketManager.disconnectAll();
});
