/* Data Scraping specific styles */

.data-scraping-container {
    background-color: #f8f9fa;
}

/* Step 1 styles */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #e9ecef;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

#shopIds {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    resize: vertical;
    min-height: 200px;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

/* Step 2 styles */
.progress-stat {
    text-align: center;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #0d6efd;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.progress {
    height: 1.5rem;
    background-color: #e9ecef;
    border-radius: 0.375rem;
}

.progress-bar {
    background-color: #0d6efd;
    transition: width 0.3s ease;
}

/* Shop progress table */
#shopProgressTable {
    font-size: 0.9rem;
}

#shopProgressTable th {
    background-color: #343a40;
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

#shopProgressTable td {
    vertical-align: middle;
    text-align: center;
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-waiting {
    background-color: #6c757d;
    color: white;
}

.status-processing {
    background-color: #ffc107;
    color: #000;
    animation: pulse 1.5s infinite;
}

.status-completed {
    background-color: #28a745;
    color: white;
}

.status-error {
    background-color: #dc3545;
    color: white;
}

.status-no_products {
    background-color: #17a2b8;
    color: white;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Log container */
.log-container {
    height: 400px;
    overflow-y: auto;
    padding: 1rem;
    background-color: #212529;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    word-wrap: break-word;
}

.log-entry.info {
    background-color: rgba(13, 110, 253, 0.1);
    border-left: 3px solid #0d6efd;
}

.log-entry.warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-left: 3px solid #ffc107;
    color: #ffc107;
}

.log-entry.error {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 3px solid #dc3545;
    color: #dc3545;
}

.log-entry.success {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 3px solid #28a745;
    color: #28a745;
}

.log-timestamp {
    color: #6c757d;
    font-size: 0.7rem;
    margin-right: 0.5rem;
}

.log-shop-id {
    color: #17a2b8;
    font-weight: bold;
    margin-right: 0.5rem;
}

/* Custom scrollbar for log container */
.log-container::-webkit-scrollbar {
    width: 8px;
}

.log-container::-webkit-scrollbar-track {
    background: #343a40;
}

.log-container::-webkit-scrollbar-thumb {
    background: #6c757d;
    border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}

/* Modal styles */
.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .progress-stat {
        margin-bottom: 1rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    #shopProgressTable {
        font-size: 0.8rem;
    }
    
    .log-container {
        height: 300px;
        font-size: 0.7rem;
    }
    
    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .data-scraping-container .row {
        margin: 0;
    }
    
    .data-scraping-container .col-12,
    .data-scraping-container .col-md-6 {
        padding: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .progress-stat {
        padding: 0.75rem;
    }
    
    .stat-value {
        font-size: 1.25rem;
    }
    
    .log-container {
        height: 250px;
    }
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.running {
    background-color: #28a745;
    animation: pulse 1.5s infinite;
}

.status-indicator.idle {
    background-color: #6c757d;
}

.status-indicator.error {
    background-color: #dc3545;
}

.status-indicator.completed {
    background-color: #17a2b8;
}

/* Table enhancements */
.table-responsive {
    border-radius: 0.375rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
}

.sticky-top {
    position: sticky;
    top: 0;
    z-index: 10;
}
