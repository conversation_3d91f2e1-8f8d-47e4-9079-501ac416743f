"""
ASGI config for Multi-Program WebUI project.
"""

import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
import apps.core.routing
import apps.data_scraping.routing

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack(
        URLRouter([
            *apps.core.routing.websocket_urlpatterns,
            *apps.data_scraping.routing.websocket_urlpatterns,
        ])
    ),
})
