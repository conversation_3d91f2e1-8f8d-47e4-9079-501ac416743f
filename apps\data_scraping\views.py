from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse, Http404
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.conf import settings
from django.utils import timezone
from apps.core.models import TabSession
from .models import ScrapingSession, ShopProgress, ScrapingLog, LoginCredentials
from .tasks import start_scraping_task
import json
import uuid
import os
import re
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile


def index(request):
    """Main view for Data Scraping"""
    tab_id = request.GET.get('tab_id')
    if not tab_id:
        return HttpResponse("Tab ID required", status=400)
    
    # Verify tab exists and belongs to current session
    tab = get_object_or_404(TabSession, 
                           tab_id=tab_id, 
                           session_key=request.session.session_key,
                           is_active=True)
    
    # Get or create scraping session
    scraping_session, created = ScrapingSession.objects.get_or_create(
        tab_id=tab_id,
        defaults={
            'session_id': uuid.uuid4(),
            'shop_ids': [],
            'output_path': os.path.join(settings.MEDIA_ROOT, 'exports'),
            'output_filename': 'scraped_data.xlsx',
            'username': 'princekiix',  # Default username
            'password': 'Beyondk@2025'  # Default password (should be encrypted)
        }
    )
    
    # Get login credentials if exists
    login_creds = LoginCredentials.objects.filter(tab_id=tab_id).first()
    
    context = {
        'tab_id': tab_id,
        'scraping_session': scraping_session,
        'login_credentials': login_creds,
    }
    
    # Determine which step to show based on session status
    if scraping_session.status in ['running', 'completed'] and scraping_session.current_step == 2:
        return render(request, 'data_scraping/step2.html', context)
    else:
        return render(request, 'data_scraping/step1.html', context)


@csrf_exempt
@require_http_methods(["POST"])
def start_processing(request):
    """Start the scraping process"""
    try:
        data = json.loads(request.body)
        tab_id = data.get('tab_id')
        shop_ids_text = data.get('shop_ids', '')
        output_path = data.get('output_path', '')
        output_filename = data.get('output_filename', 'scraped_data.xlsx')
        username = data.get('username', 'princekiix')
        password = data.get('password', 'Beyondk@2025')
        
        if not tab_id:
            return JsonResponse({'error': 'Tab ID required'}, status=400)
        
        # Parse shop IDs
        shop_ids = parse_shop_ids(shop_ids_text)
        if not shop_ids:
            return JsonResponse({'error': 'No valid shop IDs provided'}, status=400)
        
        # Get scraping session
        scraping_session = get_object_or_404(ScrapingSession, tab_id=tab_id)
        
        # Update session with new parameters
        scraping_session.shop_ids = shop_ids
        scraping_session.output_path = output_path or os.path.join(settings.MEDIA_ROOT, 'exports')
        scraping_session.output_filename = output_filename
        scraping_session.username = username
        scraping_session.password = password  # Should be encrypted in production
        scraping_session.status = 'running'
        scraping_session.current_step = 2
        scraping_session.total_shops = len(shop_ids)
        scraping_session.completed_shops = 0
        scraping_session.total_products = 0
        scraping_session.save()
        
        # Save/update login credentials
        LoginCredentials.objects.update_or_create(
            tab_id=tab_id,
            defaults={
                'username': username,
                'password': password  # Should be encrypted
            }
        )
        
        # Ensure output directory exists
        os.makedirs(scraping_session.output_path, exist_ok=True)
        
        # Start Celery task
        task = start_scraping_task.delay(
            str(scraping_session.session_id),
            shop_ids,
            username,
            password,
            scraping_session.output_path,
            output_filename
        )
        
        return JsonResponse({
            'success': True,
            'task_id': task.id,
            'session_id': str(scraping_session.session_id),
            'total_shops': len(shop_ids),
            'redirect_url': f'/data-scraping/?tab_id={tab_id}'
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@require_http_methods(["GET"])
def get_session_status(request, tab_id):
    """Get current session status"""
    try:
        scraping_session = get_object_or_404(ScrapingSession, tab_id=tab_id)
        
        # Get shop progress
        shop_progress = list(ShopProgress.objects.filter(
            session=scraping_session
        ).values('shop_id', 'status', 'product_count'))
        
        # Get recent logs
        recent_logs = list(ScrapingLog.objects.filter(
            session=scraping_session
        ).order_by('-created_at')[:50].values(
            'level', 'message', 'shop_id', 'created_at'
        ))
        
        # Calculate progress percentage
        progress_percentage = 0
        if scraping_session.total_shops > 0:
            progress_percentage = int((scraping_session.completed_shops / scraping_session.total_shops) * 100)
        
        return JsonResponse({
            'session_id': str(scraping_session.session_id),
            'status': scraping_session.status,
            'current_step': scraping_session.current_step,
            'total_shops': scraping_session.total_shops,
            'completed_shops': scraping_session.completed_shops,
            'total_products': scraping_session.total_products,
            'progress_percentage': progress_percentage,
            'shop_progress': shop_progress,
            'recent_logs': recent_logs,
            'result_file_path': scraping_session.result_file_path,
            'error_message': scraping_session.error_message,
            'started_at': scraping_session.started_at.isoformat() if scraping_session.started_at else None,
            'completed_at': scraping_session.completed_at.isoformat() if scraping_session.completed_at else None,
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def save_login_credentials(request):
    """Save login credentials"""
    try:
        data = json.loads(request.body)
        tab_id = data.get('tab_id')
        username = data.get('username')
        password = data.get('password')
        
        if not all([tab_id, username, password]):
            return JsonResponse({'error': 'Missing required fields'}, status=400)
        
        # Save credentials
        LoginCredentials.objects.update_or_create(
            tab_id=tab_id,
            defaults={
                'username': username,
                'password': password  # Should be encrypted in production
            }
        )
        
        return JsonResponse({'success': True})
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@require_http_methods(["GET"])
def get_login_credentials(request, tab_id):
    """Get saved login credentials"""
    try:
        creds = LoginCredentials.objects.filter(tab_id=tab_id).first()
        
        if creds:
            return JsonResponse({
                'username': creds.username,
                'password': creds.password  # In production, this should be decrypted
            })
        else:
            return JsonResponse({
                'username': 'princekiix',  # Default
                'password': 'Beyondk@2025'  # Default
            })
            
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@require_http_methods(["GET"])
def download_result(request, tab_id):
    """Download result file"""
    try:
        scraping_session = get_object_or_404(ScrapingSession, tab_id=tab_id)
        
        if not scraping_session.result_file_path or not os.path.exists(scraping_session.result_file_path):
            raise Http404("Result file not found")
        
        with open(scraping_session.result_file_path, 'rb') as f:
            response = HttpResponse(f.read(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            response['Content-Disposition'] = f'attachment; filename="{os.path.basename(scraping_session.result_file_path)}"'
            return response
            
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def cancel_scraping(request):
    """Cancel running scraping task"""
    try:
        data = json.loads(request.body)
        tab_id = data.get('tab_id')
        
        scraping_session = get_object_or_404(ScrapingSession, tab_id=tab_id)
        
        if scraping_session.status == 'running':
            scraping_session.status = 'cancelled'
            scraping_session.save()
            
            # TODO: Cancel Celery task if task_id is stored
            
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'error': 'No running task to cancel'}, status=400)
            
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def parse_shop_ids(shop_ids_text):
    """Parse shop IDs from text input"""
    if not shop_ids_text:
        return []
    
    # Remove numbering and extract IDs
    lines = shop_ids_text.strip().split('\n')
    shop_ids = []
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # Remove numbering (e.g., "1. ", "2. ")
        line = re.sub(r'^\d+\.\s*', '', line)
        
        # Extract numeric ID
        match = re.search(r'\d+', line)
        if match:
            shop_ids.append(match.group())
    
    return shop_ids
