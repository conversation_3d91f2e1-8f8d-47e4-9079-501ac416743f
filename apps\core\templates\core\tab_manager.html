{% extends 'base.html' %}
{% load static %}

{% block title %}Multi-Program WebUI - Tab Manager{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/tabs.css' %}">
{% endblock %}

{% block content %}
<div class="container-fluid h-100">
    <!-- Header với dropdown chọn chương trình -->
    <div class="row bg-primary text-white py-2">
        <div class="col-md-6">
            <h4 class="mb-0">
                <i class="fas fa-desktop"></i>
                Multi-Program WebUI
            </h4>
        </div>
        <div class="col-md-6 text-end">
            <div class="dropdown">
                <button class="btn btn-light dropdown-toggle" type="button" id="programDropdown" data-bs-toggle="dropdown">
                    <i class="fas fa-plus"></i> Mở chương trình
                </button>
                <ul class="dropdown-menu" id="programDropdownMenu">
                    <!-- Sẽ được populate bởi JavaScript -->
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Tab navigation -->
    <div class="row">
        <div class="col-12">
            <ul class="nav nav-tabs" id="programTabs" role="tablist">
                <!-- Tabs sẽ được tạo động bởi JavaScript -->
            </ul>
        </div>
    </div>
    
    <!-- Tab content -->
    <div class="row flex-grow-1">
        <div class="col-12 h-100">
            <div class="tab-content h-100" id="programTabContent">
                <!-- Tab content sẽ được load động -->
                <div class="tab-pane fade show active h-100" id="welcome-tab" role="tabpanel">
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center">
                            <i class="fas fa-rocket fa-5x text-primary mb-4"></i>
                            <h2>Chào mừng đến với Multi-Program WebUI</h2>
                            <p class="lead">Chọn một chương trình từ dropdown ở trên để bắt đầu</p>
                            
                            <div class="row mt-5">
                                {% for program in programs %}
                                <div class="col-md-4 mb-3">
                                    <div class="card program-card" data-program="{{ program.key }}">
                                        <div class="card-body text-center">
                                            <div class="program-icon">{{ program.icon }}</div>
                                            <h5 class="card-title">{{ program.name }}</h5>
                                            <p class="card-text">{{ program.description }}</p>
                                            <button class="btn btn-primary btn-sm open-program-btn" data-program="{{ program.key }}">
                                                Mở chương trình
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3 mb-0">Đang tải chương trình...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/tab_manager.js' %}"></script>
<script>
    // Initialize tab manager với dữ liệu từ server
    const initialTabs = [
        {% for tab in active_tabs %}
        {
            id: '{{ tab.tab_id }}',
            programName: '{{ tab.program_name }}',
            displayName: '{{ tab.program_display_name }}',
            url: '/{{ tab.program_name|slugify }}/?tab_id={{ tab.tab_id }}'
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];
    
    const availablePrograms = [
        {% for program in programs %}
        {
            key: '{{ program.key }}',
            name: '{{ program.name }}',
            description: '{{ program.description }}',
            icon: '{{ program.icon }}'
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];
    
    // Initialize when document ready
    $(document).ready(function() {
        TabManager.init(initialTabs, availablePrograms);
    });
</script>
{% endblock %}
