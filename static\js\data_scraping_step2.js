/**
 * Data Scraping Step 2 - Progress Tracking
 */

class DataScrapingStep2 {
    constructor() {
        this.tabId = null;
        this.sessionId = null;
        this.websocket = null;
        this.startTime = null;
        this.timerInterval = null;
        this.shopProgress = new Map();
        this.logs = [];
    }

    init(tabId, sessionId, websocketUrl) {
        this.tabId = tabId;
        this.sessionId = sessionId;
        
        this.setupEventListeners();
        this.setupWebSocket(websocketUrl);
        this.loadInitialStatus();
        this.startTimer();
    }

    setupEventListeners() {
        // Back button
        $('#backBtn').on('click', () => {
            window.location.href = `/data-scraping/?tab_id=${this.tabId}`;
        });

        // Cancel button
        $('#cancelBtn').on('click', () => {
            this.cancelScraping();
        });

        // Clear logs button
        $('#clearLogsBtn').on('click', () => {
            this.clearLogs();
        });

        // Download button
        $('#downloadBtn').on('click', () => {
            this.downloadResult();
        });

        // New scraping button
        $('#newScrapingBtn').on('click', () => {
            window.location.href = `/data-scraping/?tab_id=${this.tabId}`;
        });

        // Retry button
        $('#retryBtn').on('click', () => {
            window.location.href = `/data-scraping/?tab_id=${this.tabId}`;
        });
    }

    setupWebSocket(url) {
        this.websocket = window.WebSocketManager.create(`data_scraping_${this.tabId}`, url);
        
        // Setup message handlers
        this.websocket.addMessageHandler('session_status', (data) => {
            this.updateStatus(data);
        });

        this.websocket.addMessageHandler('task_update', (data) => {
            this.handleTaskUpdate(data);
        });

        this.websocket.addMessageHandler('log_message', (data) => {
            this.addLogMessage(data);
        });

        this.websocket.addMessageHandler('shop_status_update', (data) => {
            this.updateShopStatus(data);
        });

        // Setup connection handlers
        this.websocket.onOpen = () => {
            console.log('WebSocket connected');
            this.websocket.send({ type: 'get_status' });
        };

        this.websocket.onClose = () => {
            console.log('WebSocket disconnected');
        };

        this.websocket.onError = (error) => {
            console.error('WebSocket error:', error);
        };

        // Connect
        this.websocket.connect();
    }

    async loadInitialStatus() {
        try {
            const response = await fetch(`/data-scraping/api/status/${this.tabId}/`);
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            this.updateStatus(data);
        } catch (error) {
            console.error('Error loading initial status:', error);
            this.showError('Không thể tải trạng thái ban đầu: ' + error.message);
        }
    }

    updateStatus(data) {
        // Update overview stats
        $('#completedShops').text(data.completed_shops || 0);
        $('#totalProducts').text((data.total_products || 0).toLocaleString());
        $('#currentStatus').text(this.getStatusText(data.status));
        
        // Update progress bar
        const percentage = data.progress_percentage || 0;
        $('#progressBar').css('width', percentage + '%');
        $('#progressText').text(percentage + '%');
        
        // Update shop progress table
        if (data.shop_progress) {
            this.updateShopProgressTable(data.shop_progress);
        }
        
        // Handle completion or error
        if (data.status === 'completed') {
            this.handleCompletion(data);
        } else if (data.status === 'error') {
            this.handleError(data.error_message);
        }
        
        // Set start time if available
        if (data.started_at && !this.startTime) {
            this.startTime = new Date(data.started_at);
        }
    }

    handleTaskUpdate(data) {
        // Update specific fields from task update
        if (data.completed_shops !== undefined) {
            $('#completedShops').text(data.completed_shops);
        }
        
        if (data.total_products !== undefined) {
            $('#totalProducts').text(data.total_products.toLocaleString());
        }
        
        if (data.progress_percentage !== undefined) {
            $('#progressBar').css('width', data.progress_percentage + '%');
            $('#progressText').text(data.progress_percentage + '%');
        }
        
        if (data.status) {
            $('#currentStatus').text(this.getStatusText(data.status));
            
            if (data.status === 'completed') {
                this.handleCompletion(data);
            } else if (data.status === 'error') {
                this.handleError(data.error_message);
            }
        }
        
        // Update shop status if provided
        if (data.shop_id) {
            this.updateShopStatus(data);
        }
    }

    updateShopStatus(data) {
        const shopId = data.shop_id;
        const status = data.status;
        const productCount = data.product_count || 0;
        
        // Update shop progress map
        this.shopProgress.set(shopId, { status, productCount });
        
        // Update table row
        const row = $(`#shopProgressTable tbody tr[data-shop-id="${shopId}"]`);
        if (row.length > 0) {
            row.find('.shop-status').html(this.getStatusBadge(status));
            row.find('.shop-products').text(productCount);
        }
    }

    updateShopProgressTable(shopProgressData) {
        const tbody = $('#shopProgressTable tbody');
        tbody.empty();
        
        shopProgressData.forEach((shop, index) => {
            const row = $(`
                <tr data-shop-id="${shop.shop_id}">
                    <td>${index + 1}</td>
                    <td>${shop.shop_id}</td>
                    <td class="shop-status">${this.getStatusBadge(shop.status)}</td>
                    <td class="shop-products">${shop.product_count || 0}</td>
                </tr>
            `);
            tbody.append(row);
            
            // Store in map
            this.shopProgress.set(shop.shop_id, {
                status: shop.status,
                productCount: shop.product_count || 0
            });
        });
    }

    addLogMessage(data) {
        const timestamp = new Date(data.timestamp || Date.now()).toLocaleTimeString();
        const level = data.level || 'INFO';
        const message = data.message || '';
        const shopId = data.shop_id || '';
        
        const logEntry = $(`
            <div class="log-entry ${level.toLowerCase()} fade-in">
                <span class="log-timestamp">${timestamp}</span>
                ${shopId ? `<span class="log-shop-id">[${shopId}]</span>` : ''}
                <span class="log-message">${message}</span>
            </div>
        `);
        
        const container = $('#logContainer');
        container.append(logEntry);
        
        // Auto-scroll to bottom
        container.scrollTop(container[0].scrollHeight);
        
        // Limit log entries to prevent memory issues
        const entries = container.find('.log-entry');
        if (entries.length > 1000) {
            entries.slice(0, entries.length - 1000).remove();
        }
        
        // Store in array
        this.logs.push({ timestamp, level, message, shopId });
    }

    getStatusText(status) {
        const statusMap = {
            'idle': 'Chờ xử lý',
            'running': 'Đang chạy',
            'completed': 'Hoàn thành',
            'error': 'Lỗi',
            'cancelled': 'Đã hủy'
        };
        return statusMap[status] || status;
    }

    getStatusBadge(status) {
        const badges = {
            'waiting': '<span class="status-badge status-waiting">⏳ Chờ</span>',
            'processing': '<span class="status-badge status-processing">🔄 Xử lý</span>',
            'completed': '<span class="status-badge status-completed">✅ Xong</span>',
            'error': '<span class="status-badge status-error">❌ Lỗi</span>',
            'no_products': '<span class="status-badge status-no_products">ℹ️ Trống</span>'
        };
        return badges[status] || `<span class="status-badge">${status}</span>`;
    }

    startTimer() {
        this.timerInterval = setInterval(() => {
            if (this.startTime) {
                const elapsed = Date.now() - this.startTime.getTime();
                const minutes = Math.floor(elapsed / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                $('#elapsedTime').text(`${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
            }
        }, 1000);
    }

    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }

    handleCompletion(data) {
        this.stopTimer();
        
        // Update final counts
        $('#finalProductCount').text((data.total_products || 0).toLocaleString());
        $('#finalShopCount').text(data.completed_shops || 0);
        
        // Show result modal
        $('#resultModal').modal('show');
    }

    handleError(errorMessage) {
        this.stopTimer();
        
        $('#errorMessage').text(errorMessage || 'Lỗi không xác định');
        $('#errorModal').modal('show');
    }

    async cancelScraping() {
        if (!confirm('Bạn có chắc chắn muốn hủy quá trình scraping?')) {
            return;
        }
        
        try {
            const response = await fetch('/data-scraping/api/cancel/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': window.CSRF_TOKEN
                },
                body: JSON.stringify({
                    tab_id: this.tabId
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showAlert('Đã hủy quá trình scraping', 'info');
                $('#currentStatus').text('Đã hủy');
            } else {
                throw new Error(data.error || 'Failed to cancel');
            }
        } catch (error) {
            console.error('Error cancelling scraping:', error);
            this.showAlert('Lỗi khi hủy: ' + error.message, 'danger');
        }
    }

    downloadResult() {
        window.open(`/data-scraping/api/download/${this.tabId}/`, '_blank');
    }

    clearLogs() {
        $('#logContainer').empty();
        this.logs = [];
    }

    showAlert(message, type = 'info') {
        // Remove existing alerts
        $('.alert').remove();

        const alertClass = `alert-${type}`;
        const alert = $(`
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);

        // Add to top of container
        $('.data-scraping-container').prepend(alert);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            alert.alert('close');
        }, 5000);
    }

    showError(message) {
        this.showAlert(message, 'danger');
    }

    // Cleanup when leaving page
    destroy() {
        this.stopTimer();
        if (this.websocket) {
            this.websocket.disconnect();
        }
    }
}

// Global instance
window.DataScrapingStep2 = new DataScrapingStep2();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.DataScrapingStep2) {
        window.DataScrapingStep2.destroy();
    }
});
