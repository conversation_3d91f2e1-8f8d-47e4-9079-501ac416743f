#!/bin/bash

echo "========================================"
echo "  Multi-Program WebUI Launcher"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.8+ from your package manager"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "launcher.py" ]; then
    echo "ERROR: launcher.py not found"
    echo "Please run this script from the project directory"
    exit 1
fi

echo "Starting Multi-Program WebUI Launcher..."
echo

# Run the launcher
python3 launcher.py

# If launcher exits with error, show message
if [ $? -ne 0 ]; then
    echo
    echo "Launcher exited with error. Check the message above."
    read -p "Press Enter to continue..."
fi
