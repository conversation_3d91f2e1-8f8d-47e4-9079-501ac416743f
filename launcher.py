#!/usr/bin/env python3
"""
Multi-Program WebUI Launcher
One-click solution to start the entire system
"""

import sys
import os
import subprocess
import threading
import time
import requests
import webbrowser
from pathlib import Path
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTextEdit, QProgressBar, QSystemTrayIcon,
    QMenu, QMessageBox, QSplashScreen, QFrame
)
from PyQt6.QtCore import QThread, pyqtSignal, QTimer, Qt, QSize
from PyQt6.QtGui import QFont, QPixmap, QIcon, QPalette, QColor

# Configuration
REDIS_PORT = 6379
DJANGO_PORT = 8000
PROJECT_DIR = Path(__file__).parent
VENV_DIR = PROJECT_DIR / "venv"
REQUIREMENTS_FILE = PROJECT_DIR / "requirements.txt"

class ServiceManager:
    """Manages all system services"""

    def __init__(self):
        self.processes = {}
        self.redis_process = None
        self.celery_process = None
        self.django_process = None

    def is_port_available(self, port):
        """Check if port is available"""
        import socket
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(('localhost', port)) != 0

    def is_redis_running(self):
        """Check if Redis is running"""
        try:
            import redis
            r = redis.Redis(host='localhost', port=REDIS_PORT, socket_connect_timeout=1)
            r.ping()
            return True
        except:
            return False

    def is_django_running(self):
        """Check if Django is running"""
        try:
            response = requests.get(f'http://localhost:{DJANGO_PORT}', timeout=2)
            return response.status_code == 200
        except:
            return False

    def start_redis(self):
        """Start Redis server"""
        if self.is_redis_running():
            return True, "Redis already running"

        try:
            # Try to start Redis
            if sys.platform == "win32":
                redis_cmd = ["redis-server.exe"]
            else:
                redis_cmd = ["redis-server"]

            self.redis_process = subprocess.Popen(
                redis_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
            )

            # Wait a bit and check if it's running
            time.sleep(2)
            if self.is_redis_running():
                return True, "Redis started successfully"
            else:
                return False, "Redis failed to start"

        except FileNotFoundError:
            return False, "Redis not found. Please install Redis server."
        except Exception as e:
            return False, f"Error starting Redis: {str(e)}"

    def start_celery(self):
        """Start Celery worker"""
        try:
            python_exe = self.get_python_executable()
            celery_cmd = [
                python_exe, "-m", "celery",
                "-A", "celery_app", "worker",
                "--loglevel=info", "--pool=solo"
            ]

            self.celery_process = subprocess.Popen(
                celery_cmd,
                cwd=PROJECT_DIR,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
            )

            return True, "Celery worker started"

        except Exception as e:
            return False, f"Error starting Celery: {str(e)}"

    def start_django(self):
        """Start Django server"""
        try:
            python_exe = self.get_python_executable()
            django_cmd = [
                python_exe, "manage.py", "runserver",
                f"localhost:{DJANGO_PORT}", "--noreload"
            ]

            self.django_process = subprocess.Popen(
                django_cmd,
                cwd=PROJECT_DIR,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
            )

            return True, "Django server started"

        except Exception as e:
            return False, f"Error starting Django: {str(e)}"

    def get_python_executable(self):
        """Get Python executable path"""
        if VENV_DIR.exists():
            if sys.platform == "win32":
                return str(VENV_DIR / "Scripts" / "python.exe")
            else:
                return str(VENV_DIR / "bin" / "python")
        return sys.executable

    def stop_all(self):
        """Stop all services"""
        stopped = []

        if self.django_process:
            try:
                self.django_process.terminate()
                self.django_process.wait(timeout=5)
                stopped.append("Django")
            except:
                try:
                    self.django_process.kill()
                    stopped.append("Django (forced)")
                except:
                    pass

        if self.celery_process:
            try:
                self.celery_process.terminate()
                self.celery_process.wait(timeout=5)
                stopped.append("Celery")
            except:
                try:
                    self.celery_process.kill()
                    stopped.append("Celery (forced)")
                except:
                    pass

        if self.redis_process:
            try:
                self.redis_process.terminate()
                self.redis_process.wait(timeout=5)
                stopped.append("Redis")
            except:
                try:
                    self.redis_process.kill()
                    stopped.append("Redis (forced)")
                except:
                    pass

        return stopped


class SetupThread(QThread):
    """Thread for initial setup"""
    progress = pyqtSignal(int)
    status = pyqtSignal(str)
    finished = pyqtSignal(bool, str)

    def __init__(self):
        super().__init__()
        self.service_manager = ServiceManager()

    def run(self):
        try:
            # Step 1: Check Python environment
            self.status.emit("🐍 Checking Python environment...")
            self.progress.emit(10)

            if not self.check_python_environment():
                self.finished.emit(False, "Python environment check failed")
                return

            # Step 2: Install dependencies
            self.status.emit("📦 Installing dependencies...")
            self.progress.emit(30)

            if not self.install_dependencies():
                self.finished.emit(False, "Failed to install dependencies")
                return

            # Step 3: Setup database
            self.status.emit("🗄️ Setting up database...")
            self.progress.emit(50)

            if not self.setup_database():
                self.finished.emit(False, "Database setup failed")
                return

            # Step 4: Create directories
            self.status.emit("📁 Creating directories...")
            self.progress.emit(70)

            self.create_directories()

            # Step 5: Collect static files
            self.status.emit("🎨 Collecting static files...")
            self.progress.emit(90)

            if not self.collect_static():
                self.finished.emit(False, "Static files collection failed")
                return

            self.progress.emit(100)
            self.status.emit("✅ Setup completed successfully!")
            self.finished.emit(True, "Setup completed")

        except Exception as e:
            self.finished.emit(False, f"Setup error: {str(e)}")

    def check_python_environment(self):
        """Check if Python environment is ready"""
        try:
            # Check if virtual environment exists
            if not VENV_DIR.exists():
                self.status.emit("Creating virtual environment...")
                subprocess.run([
                    sys.executable, "-m", "venv", str(VENV_DIR)
                ], check=True, cwd=PROJECT_DIR)

            return True
        except Exception as e:
            self.status.emit(f"Python environment error: {str(e)}")
            return False

    def install_dependencies(self):
        """Install Python dependencies"""
        try:
            python_exe = self.service_manager.get_python_executable()

            # Upgrade pip first
            subprocess.run([
                python_exe, "-m", "pip", "install", "--upgrade", "pip"
            ], check=True, cwd=PROJECT_DIR,
               stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

            # Install requirements
            if REQUIREMENTS_FILE.exists():
                subprocess.run([
                    python_exe, "-m", "pip", "install", "-r", str(REQUIREMENTS_FILE)
                ], check=True, cwd=PROJECT_DIR,
                   stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

            return True
        except Exception as e:
            self.status.emit(f"Dependencies installation error: {str(e)}")
            return False

    def setup_database(self):
        """Setup Django database"""
        try:
            python_exe = self.service_manager.get_python_executable()

            # Make migrations
            subprocess.run([
                python_exe, "manage.py", "makemigrations"
            ], check=True, cwd=PROJECT_DIR,
               stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

            # Run migrations
            subprocess.run([
                python_exe, "manage.py", "migrate"
            ], check=True, cwd=PROJECT_DIR,
               stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

            return True
        except Exception as e:
            self.status.emit(f"Database setup error: {str(e)}")
            return False

    def create_directories(self):
        """Create necessary directories"""
        directories = [
            PROJECT_DIR / "logs",
            PROJECT_DIR / "media",
            PROJECT_DIR / "media" / "exports",
            PROJECT_DIR / "staticfiles"
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

    def collect_static(self):
        """Collect Django static files"""
        try:
            python_exe = self.service_manager.get_python_executable()
            subprocess.run([
                python_exe, "manage.py", "collectstatic", "--noinput"
            ], check=True, cwd=PROJECT_DIR,
               stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

            return True
        except Exception as e:
            self.status.emit(f"Static files error: {str(e)}")
            return False


class StartupThread(QThread):
    """Thread for starting services"""
    progress = pyqtSignal(int)
    status = pyqtSignal(str)
    finished = pyqtSignal(bool, str)

    def __init__(self, service_manager):
        super().__init__()
        self.service_manager = service_manager

    def run(self):
        try:
            # Step 1: Start Redis
            self.status.emit("🔴 Starting Redis server...")
            self.progress.emit(25)

            success, message = self.service_manager.start_redis()
            if not success:
                self.finished.emit(False, f"Redis: {message}")
                return

            # Step 2: Start Celery
            self.status.emit("🔄 Starting Celery worker...")
            self.progress.emit(50)

            success, message = self.service_manager.start_celery()
            if not success:
                self.finished.emit(False, f"Celery: {message}")
                return

            # Step 3: Start Django
            self.status.emit("🌐 Starting Django server...")
            self.progress.emit(75)

            success, message = self.service_manager.start_django()
            if not success:
                self.finished.emit(False, f"Django: {message}")
                return

            # Step 4: Wait for Django to be ready
            self.status.emit("⏳ Waiting for services to be ready...")
            self.progress.emit(90)

            # Wait up to 30 seconds for Django to start
            for i in range(30):
                if self.service_manager.is_django_running():
                    break
                time.sleep(1)

            if not self.service_manager.is_django_running():
                self.finished.emit(False, "Django server failed to start properly")
                return

            self.progress.emit(100)
            self.status.emit("✅ All services started successfully!")
            self.finished.emit(True, "All services running")

        except Exception as e:
            self.finished.emit(False, f"Startup error: {str(e)}")


class StatusThread(QThread):
    """Thread for monitoring service status"""
    status_update = pyqtSignal(dict)

    def __init__(self, service_manager):
        super().__init__()
        self.service_manager = service_manager
        self.running = True

    def run(self):
        while self.running:
            status = {
                'redis': self.service_manager.is_redis_running(),
                'django': self.service_manager.is_django_running(),
                'celery': self.service_manager.celery_process is not None and
                         self.service_manager.celery_process.poll() is None
            }
            self.status_update.emit(status)
            time.sleep(5)  # Check every 5 seconds

    def stop(self):
        self.running = False


class MainWindow(QMainWindow):
    """Main launcher window"""

    def __init__(self):
        super().__init__()
        self.service_manager = ServiceManager()
        self.setup_thread = None
        self.startup_thread = None
        self.status_thread = None
        self.is_setup_complete = False

        self.init_ui()
        self.check_initial_setup()

    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("Multi-Program WebUI Launcher")
        self.setFixedSize(600, 500)
        self.setStyleSheet(self.get_stylesheet())

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # Header
        self.create_header(layout)

        # Status section
        self.create_status_section(layout)

        # Progress section
        self.create_progress_section(layout)

        # Control buttons
        self.create_control_section(layout)

        # Log section
        self.create_log_section(layout)

        # System tray
        self.create_system_tray()

    def create_header(self, layout):
        """Create header section"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QVBoxLayout(header_frame)

        title = QLabel("🚀 Multi-Program WebUI")
        title.setObjectName("titleLabel")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)

        subtitle = QLabel("One-click solution to start all services")
        subtitle.setObjectName("subtitleLabel")
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)

        header_layout.addWidget(title)
        header_layout.addWidget(subtitle)
        layout.addWidget(header_frame)

    def create_status_section(self, layout):
        """Create service status section"""
        status_frame = QFrame()
        status_frame.setObjectName("statusFrame")
        status_layout = QHBoxLayout(status_frame)

        # Redis status
        self.redis_status = QLabel("🔴 Redis: Stopped")
        self.redis_status.setObjectName("statusLabel")

        # Celery status
        self.celery_status = QLabel("🔄 Celery: Stopped")
        self.celery_status.setObjectName("statusLabel")

        # Django status
        self.django_status = QLabel("🌐 Django: Stopped")
        self.django_status.setObjectName("statusLabel")

        status_layout.addWidget(self.redis_status)
        status_layout.addWidget(self.celery_status)
        status_layout.addWidget(self.django_status)

        layout.addWidget(status_frame)

    def create_progress_section(self, layout):
        """Create progress section"""
        progress_frame = QFrame()
        progress_layout = QVBoxLayout(progress_frame)

        self.progress_label = QLabel("Ready to start")
        self.progress_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        progress_layout.addWidget(self.progress_label)
        progress_layout.addWidget(self.progress_bar)

        layout.addWidget(progress_frame)

    def create_control_section(self, layout):
        """Create control buttons section"""
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)

        # Setup button
        self.setup_btn = QPushButton("🔧 Setup System")
        self.setup_btn.setObjectName("setupButton")
        self.setup_btn.clicked.connect(self.start_setup)

        # Start button
        self.start_btn = QPushButton("▶️ Start Services")
        self.start_btn.setObjectName("startButton")
        self.start_btn.clicked.connect(self.start_services)
        self.start_btn.setEnabled(False)

        # Open browser button
        self.browser_btn = QPushButton("🌐 Open WebUI")
        self.browser_btn.setObjectName("browserButton")
        self.browser_btn.clicked.connect(self.open_browser)
        self.browser_btn.setEnabled(False)

        # Stop button
        self.stop_btn = QPushButton("⏹️ Stop All")
        self.stop_btn.setObjectName("stopButton")
        self.stop_btn.clicked.connect(self.stop_services)
        self.stop_btn.setEnabled(False)

        control_layout.addWidget(self.setup_btn)
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.browser_btn)
        control_layout.addWidget(self.stop_btn)

        layout.addWidget(control_frame)

    def create_log_section(self, layout):
        """Create log section"""
        log_frame = QFrame()
        log_layout = QVBoxLayout(log_frame)

        log_label = QLabel("📋 Activity Log:")
        log_label.setObjectName("logLabel")

        self.log_text = QTextEdit()
        self.log_text.setObjectName("logText")
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)

        log_layout.addWidget(log_label)
        log_layout.addWidget(self.log_text)

        layout.addWidget(log_frame)

    def create_system_tray(self):
        """Create system tray icon"""
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.tray_icon = QSystemTrayIcon(self)

            # Create tray menu
            tray_menu = QMenu()

            show_action = tray_menu.addAction("Show Launcher")
            show_action.triggered.connect(self.show)

            browser_action = tray_menu.addAction("Open WebUI")
            browser_action.triggered.connect(self.open_browser)

            tray_menu.addSeparator()

            quit_action = tray_menu.addAction("Quit")
            quit_action.triggered.connect(self.quit_application)

            self.tray_icon.setContextMenu(tray_menu)
            self.tray_icon.activated.connect(self.tray_icon_activated)

            # Set icon (you can replace with actual icon file)
            self.tray_icon.setIcon(self.style().standardIcon(self.style().StandardPixmap.SP_ComputerIcon))
            self.tray_icon.show()

    def tray_icon_activated(self, reason):
        """Handle tray icon activation"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.show()
            self.raise_()
            self.activateWindow()

    def check_initial_setup(self):
        """Check if initial setup is needed"""
        # Check if database exists
        db_file = PROJECT_DIR / "db.sqlite3"
        if db_file.exists():
            self.is_setup_complete = True
            self.setup_btn.setText("🔧 Re-setup System")
            self.start_btn.setEnabled(True)
            self.log("✅ System appears to be set up. Ready to start services.")
        else:
            self.log("⚠️ Initial setup required. Click 'Setup System' to begin.")

    def start_setup(self):
        """Start the setup process"""
        if self.setup_thread and self.setup_thread.isRunning():
            return

        self.log("🔧 Starting system setup...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.setup_btn.setEnabled(False)

        self.setup_thread = SetupThread()
        self.setup_thread.progress.connect(self.progress_bar.setValue)
        self.setup_thread.status.connect(self.progress_label.setText)
        self.setup_thread.finished.connect(self.setup_finished)
        self.setup_thread.start()

    def setup_finished(self, success, message):
        """Handle setup completion"""
        self.progress_bar.setVisible(False)
        self.setup_btn.setEnabled(True)

        if success:
            self.is_setup_complete = True
            self.setup_btn.setText("🔧 Re-setup System")
            self.start_btn.setEnabled(True)
            self.log(f"✅ Setup completed: {message}")
            self.progress_label.setText("Setup completed! Ready to start services.")
        else:
            self.log(f"❌ Setup failed: {message}")
            self.progress_label.setText("Setup failed. Check log for details.")
            QMessageBox.critical(self, "Setup Failed", f"Setup failed:\n{message}")

    def start_services(self):
        """Start all services"""
        if not self.is_setup_complete:
            QMessageBox.warning(self, "Setup Required", "Please run system setup first.")
            return

        if self.startup_thread and self.startup_thread.isRunning():
            return

        self.log("🚀 Starting all services...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

        self.startup_thread = StartupThread(self.service_manager)
        self.startup_thread.progress.connect(self.progress_bar.setValue)
        self.startup_thread.status.connect(self.progress_label.setText)
        self.startup_thread.finished.connect(self.startup_finished)
        self.startup_thread.start()

        # Start status monitoring
        if not self.status_thread or not self.status_thread.isRunning():
            self.status_thread = StatusThread(self.service_manager)
            self.status_thread.status_update.connect(self.update_service_status)
            self.status_thread.start()

    def startup_finished(self, success, message):
        """Handle startup completion"""
        self.progress_bar.setVisible(False)
        self.start_btn.setEnabled(True)

        if success:
            self.log(f"✅ All services started: {message}")
            self.progress_label.setText("All services running! WebUI is ready.")
            self.browser_btn.setEnabled(True)

            # Show notification
            if hasattr(self, 'tray_icon'):
                self.tray_icon.showMessage(
                    "Multi-Program WebUI",
                    "All services started successfully!\nWebUI is ready to use.",
                    QSystemTrayIcon.MessageIcon.Information,
                    3000
                )
        else:
            self.log(f"❌ Startup failed: {message}")
            self.progress_label.setText("Startup failed. Check log for details.")
            self.stop_btn.setEnabled(False)
            QMessageBox.critical(self, "Startup Failed", f"Failed to start services:\n{message}")

    def stop_services(self):
        """Stop all services"""
        self.log("⏹️ Stopping all services...")

        # Stop status monitoring
        if self.status_thread:
            self.status_thread.stop()
            self.status_thread.wait()

        # Stop services
        stopped = self.service_manager.stop_all()

        if stopped:
            self.log(f"✅ Stopped services: {', '.join(stopped)}")
        else:
            self.log("ℹ️ No services were running")

        # Update UI
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.browser_btn.setEnabled(False)
        self.progress_label.setText("All services stopped")

        # Update status labels
        self.redis_status.setText("🔴 Redis: Stopped")
        self.celery_status.setText("🔄 Celery: Stopped")
        self.django_status.setText("🌐 Django: Stopped")

    def open_browser(self):
        """Open the WebUI in browser"""
        if self.service_manager.is_django_running():
            webbrowser.open(f'http://localhost:{DJANGO_PORT}')
            self.log("🌐 Opened WebUI in browser")
        else:
            QMessageBox.warning(self, "Service Not Running", "Django server is not running. Please start services first.")

    def update_service_status(self, status):
        """Update service status indicators"""
        # Redis status
        if status['redis']:
            self.redis_status.setText("🟢 Redis: Running")
        else:
            self.redis_status.setText("🔴 Redis: Stopped")

        # Celery status
        if status['celery']:
            self.celery_status.setText("🟢 Celery: Running")
        else:
            self.celery_status.setText("🔄 Celery: Stopped")

        # Django status
        if status['django']:
            self.django_status.setText("🟢 Django: Running")
        else:
            self.django_status.setText("🌐 Django: Stopped")

    def log(self, message):
        """Add message to log"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_text.append(log_message)

        # Auto-scroll to bottom
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def closeEvent(self, event):
        """Handle window close event"""
        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            # Minimize to tray instead of closing
            self.hide()
            self.tray_icon.showMessage(
                "Multi-Program WebUI",
                "Application was minimized to tray",
                QSystemTrayIcon.MessageIcon.Information,
                2000
            )
            event.ignore()
        else:
            self.quit_application()

    def quit_application(self):
        """Quit the application"""
        # Stop all services
        if self.service_manager:
            self.service_manager.stop_all()

        # Stop threads
        if self.status_thread:
            self.status_thread.stop()
            self.status_thread.wait()

        QApplication.quit()

    def get_stylesheet(self):
        """Get the application stylesheet"""
        return """
        QMainWindow {
            background-color: #f5f5f5;
        }

        #headerFrame {
            background-color: #2c3e50;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 10px;
        }

        #titleLabel {
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        #subtitleLabel {
            color: #bdc3c7;
            font-size: 14px;
        }

        #statusFrame {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
        }

        #statusLabel {
            font-size: 12px;
            font-weight: bold;
            padding: 8px;
            border-radius: 5px;
            background-color: #ecf0f1;
            margin: 2px;
        }

        QPushButton {
            font-size: 12px;
            font-weight: bold;
            padding: 10px 15px;
            border: none;
            border-radius: 6px;
            margin: 2px;
        }

        #setupButton {
            background-color: #f39c12;
            color: white;
        }

        #setupButton:hover {
            background-color: #e67e22;
        }

        #setupButton:disabled {
            background-color: #bdc3c7;
        }

        #startButton {
            background-color: #27ae60;
            color: white;
        }

        #startButton:hover {
            background-color: #229954;
        }

        #startButton:disabled {
            background-color: #bdc3c7;
        }

        #browserButton {
            background-color: #3498db;
            color: white;
        }

        #browserButton:hover {
            background-color: #2980b9;
        }

        #browserButton:disabled {
            background-color: #bdc3c7;
        }

        #stopButton {
            background-color: #e74c3c;
            color: white;
        }

        #stopButton:hover {
            background-color: #c0392b;
        }

        #stopButton:disabled {
            background-color: #bdc3c7;
        }

        QProgressBar {
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
        }

        QProgressBar::chunk {
            background-color: #3498db;
            border-radius: 3px;
        }

        #logLabel {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        #logText {
            background-color: #2c3e50;
            color: #ecf0f1;
            border: 1px solid #34495e;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            padding: 10px;
        }
        """


class SplashScreen(QSplashScreen):
    """Splash screen for application startup"""

    def __init__(self):
        # Create a simple pixmap for splash screen
        pixmap = QPixmap(400, 300)
        pixmap.fill(QColor(44, 62, 80))  # Dark blue background

        super().__init__(pixmap)

        # Add text to splash screen
        self.setStyleSheet("""
            QSplashScreen {
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
        """)

        self.showMessage(
            "🚀 Multi-Program WebUI Launcher\n\nInitializing...",
            Qt.AlignmentFlag.AlignCenter,
            Qt.GlobalColor.white
        )


def check_dependencies():
    """Check if required dependencies are available"""
    missing_deps = []

    # Check PyQt6
    try:
        from PyQt6.QtWidgets import QApplication
    except ImportError:
        missing_deps.append("PyQt6")

    # Check requests
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")

    return missing_deps


def install_launcher_dependencies():
    """Install launcher-specific dependencies"""
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "PyQt6", "requests"
        ], check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        return True
    except:
        return False


def main():
    """Main application entry point"""
    # Check dependencies first
    missing_deps = check_dependencies()
    if missing_deps:
        print(f"Missing dependencies: {', '.join(missing_deps)}")
        print("Installing launcher dependencies...")

        if install_launcher_dependencies():
            print("Dependencies installed successfully!")
            # Restart the script
            os.execv(sys.executable, [sys.executable] + sys.argv)
        else:
            print("Failed to install dependencies. Please install manually:")
            print("pip install PyQt6 requests")
            sys.exit(1)

    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("Multi-Program WebUI Launcher")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Multi-Program WebUI")

    # Show splash screen
    splash = SplashScreen()
    splash.show()
    app.processEvents()

    # Create main window
    window = MainWindow()

    # Close splash and show main window
    splash.finish(window)
    window.show()

    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
