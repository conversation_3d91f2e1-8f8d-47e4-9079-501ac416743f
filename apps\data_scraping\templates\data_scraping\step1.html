{% extends 'base.html' %}
{% load static %}

{% block title %}Data Scraping - Nhập thông tin{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/data_scraping.css' %}">
{% endblock %}

{% block content %}
<div class="container-fluid h-100 data-scraping-container">
    <!-- Header -->
    <div class="row bg-primary text-white py-2">
        <div class="col-12">
            <h4 class="mb-0">
                <i class="fas fa-search"></i>
                Data Scraping - Nhập thông tin
            </h4>
        </div>
    </div>
    
    <!-- Main content -->
    <div class="row flex-grow-1 p-3">
        <div class="col-12">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">Bước 1: Cấu hình scraping</h5>
                </div>
                <div class="card-body">
                    <form id="scrapingForm">
                        <!-- File output settings -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="outputPath" class="form-label">Nơi lưu file:</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="outputPath" 
                                           value="{{ scraping_session.output_path }}" readonly>
                                    <button class="btn btn-outline-secondary" type="button" id="browseBtn">
                                        <i class="fas fa-folder-open"></i> Browse
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="outputFilename" class="form-label">Tên file Excel:</label>
                                <input type="text" class="form-control" id="outputFilename" 
                                       value="{{ scraping_session.output_filename }}" placeholder="scraped_data.xlsx">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="button" class="btn btn-info d-block w-100" id="loginBtn">
                                    <i class="fas fa-key"></i> Đăng nhập
                                </button>
                            </div>
                        </div>
                        
                        <!-- Shop IDs input -->
                        <div class="mb-3">
                            <label for="shopIds" class="form-label">Danh sách ID shop:</label>
                            <textarea class="form-control" id="shopIds" rows="10" 
                                      placeholder="Nhập ID shop ở đây hoặc dán từ clipboard...">{% for shop_id in scraping_session.shop_ids %}{{ forloop.counter }}. {{ shop_id }}
{% endfor %}</textarea>
                            <div class="form-text">
                                Mỗi ID shop trên một dòng. Có thể có số thứ tự phía trước.
                            </div>
                        </div>
                        
                        <!-- Action buttons -->
                        <div class="row">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-secondary me-2" id="pasteBtn">
                                    <i class="fas fa-clipboard"></i> Dán từ Clipboard
                                </button>
                                <button type="button" class="btn btn-warning" id="clearBtn">
                                    <i class="fas fa-trash"></i> Xóa tất cả
                                </button>
                            </div>
                            <div class="col-md-6 text-end">
                                <button type="submit" class="btn btn-primary btn-lg" id="startBtn">
                                    <i class="fas fa-play"></i> Bắt đầu xử lý
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Login Modal -->
<div class="modal fade" id="loginModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thông tin đăng nhập Autoshopee</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="loginForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">Tên đăng nhập:</label>
                        <input type="text" class="form-control" id="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Mật khẩu:</label>
                        <input type="password" class="form-control" id="password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" id="saveLoginBtn">
                    <i class="fas fa-save"></i> Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>Đang khởi tạo quá trình scraping...</h5>
                <p class="text-muted">Vui lòng đợi trong giây lát</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/data_scraping_step1.js' %}"></script>
<script>
    // Initialize with tab data
    const TAB_ID = '{{ tab_id }}';
    const INITIAL_CREDENTIALS = {
        username: '{{ login_credentials.username|default:"princekiix" }}',
        password: '{{ login_credentials.password|default:"Beyondk@2025" }}'
    };
    
    $(document).ready(function() {
        DataScrapingStep1.init(TAB_ID, INITIAL_CREDENTIALS);
    });
</script>
{% endblock %}
